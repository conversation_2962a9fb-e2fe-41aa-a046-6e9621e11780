{"album": {"albumLongPress": {"accessDenied": "Anda tidak diizinkan untuk menghapus tugas ini!", "addImage": "Tambahkan Foto/Video", "copy": "<PERSON><PERSON><PERSON>an", "delete": "Menghapus album", "deleteContent": "Se<PERSON>a foto dalam album akan dihapus selamanya. Apakah Anda ingin menghapus album ini?", "deleted": "Album dan semua media telah dihapus!", "edit": "Mengedit informasi", "notExists": "Album tidak ada", "turnOffNotify": "Mematikan notifikasi tentang album ini", "turnOnNotify": "Mengaktifkan notifikasi album"}, "create": {"create_button": "Membuat album", "desc_placeholder": "<PERSON><PERSON><PERSON><PERSON> album...", "name_placeholder": "Ma<PERSON>kka<PERSON> nama album...", "privacy": {"friend": "<PERSON><PERSON>", "in_workspace": "<PERSON><PERSON> organ<PERSON>", "only_me": "<PERSON><PERSON> saya", "title": "P<PERSON><PERSON><PERSON>"}, "title": "Membuat album baru"}, "detail": {"add_new": "Tambahkan Foto/Video", "bottom_sheet_copy_url": "<PERSON><PERSON><PERSON>an", "bottom_sheet_share_post": "<PERSON><PERSON>", "bottom_sheet_title": "Bagikan album", "comment": "Komentar", "empty_asset": "Album kosong", "like": "<PERSON><PERSON>", "share": "Bagikan", "snack_bar_save_file_fail": "file tidak bisa disimpan!", "snack_bar_save_file_success": "File Anda ber<PERSON>il disimpan!", "snack_bar_save_image_fail": "Foto tidak bisa disimpan!", "snack_bar_save_image_success": "Foto berhasil disimpan!", "snack_bar_save_video_fail": "Video tidak bisa disimpan", "snack_bar_save_video_success": "Video berhasil disimpan"}, "edit": {"save": "Simpan", "title": "Mengedit album"}, "list": {"addImage": "Tambahkan Foto / Video", "album": "Album", "allImage": "<PERSON><PERSON><PERSON> foto", "child": "Foto-foto", "image": "Foto", "imageEmpty": "Tidak ada foto yang diunggah!"}, "mediaLongPress": {"accessDenied": "Anda tidak diizinkan untuk menghapus %1s ini!", "copy": "<PERSON><PERSON><PERSON> tautan %1s", "delete": "Menghapus %1s ini", "deleteConfirm": "%1s ini akan di<PERSON>, <PERSON><PERSON> ingin men<PERSON> %2s ini?", "deleted": "%1s telah di<PERSON>!", "download": "<PERSON><PERSON><PERSON>", "downloadError": "Tidak dapat mengunduh %1s ini", "downloaded": "%1s <PERSON>a telah di<PERSON>!", "image": "Foto", "notExists": "%1s tidak ada!", "video": "Video"}}, "alert": {"cancel": "Pembatalan", "dismiss": "Pembatalan", "later": "<PERSON><PERSON>", "ok": "OK", "skip": "Melewatkan", "title": "Notif<PERSON><PERSON>"}, "archive": {"archiveFolderSuccess": "Folder telah disimpan", "archiveProjectSuccess": "Proyek telah disimpan", "archiveTaskListSuccess": "Daftar tugas telah disimpan", "archiveTaskSuccess": "Tugas telah disimpan", "confirmArchiveFolder": {"content": "Anda yakin ingin menyimpan folder ini? Folder ini dapat dipulihkan dari", "content1": "di layar daftar proyek.", "title": "Menyimpan folder"}, "confirmArchiveProject": {"content": "Anda yakin ingin menyimpan proyek ini? Proyek ini dapat dipulihkan dari", "content1": "di layar daftar proyek.", "title": "Menyimpan proyek"}, "confirmArchiveTask": {"content": "Anda yakin ingin memulihkan tugas ini? Tugas ini akan dipulihkan", "content1": "di layar daftar proyek", "title": "Menyimpan tugas"}, "confirmArchiveTaskList": {"content": "Anda yakin ingin menyimpan daftar tugas ini? Daftar tugas ini dapat dipulihkan dari", "content1": "di layar daftar tugas", "title": "Menyimpan daftar tugas"}, "confirmUnArchiveFolder": {"content": "Anda yakin akan memulihkan folder ini? Semua tugas dan daftar tugas di folder ini akan dipulihkan.", "title": "Memulihkan folder"}, "confirmUnArchiveProject": {"content": "Anda yakin akan memulihkan proyek ini? Semua folder, daftar tugas, dan tugas proyek ini akan dipulihkan.", "title": "Memulihkan proyek"}, "confirmUnArchiveTask": {"content": "Anda yakin ingin memulihkan tugas ini? Tugas ini akan dipulihkan", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON> tugas"}, "confirmUnArchiveTaskList": {"content": "Anda yakin ingin memulihkan daftar tugas ini? Daftar tugas ini dan semua tugas di dalamnya akan dipulihkan", "title": "Memulihkan daftar tugas"}, "emptyProjectTitle": "Tidak ada pun di sini", "emptyTitle": "Tidak ada penyimpanan mana pun di sini", "title": "Daftar penyimp<PERSON>n", "unarchiveFolderSuccess": "Folder telah dipulihkan", "unarchiveProjectSuccess": "Proyek telah dipulihkan", "unarchiveTaskListSuccess": "Daftar tugas telah dipulihkan", "unarchiveTaskSuccess": "Tugas telah dipuli<PERSON>kan"}, "calendar": {"all": "<PERSON><PERSON><PERSON>", "and": "dan", "button_add_email": "Tambahkan email", "button_add_invitee": "Tambahkan partisipasi", "button_appbar_cancel": "Membatalkan", "button_create": "Membuat", "calendar_comparison": "<PERSON><PERSON><PERSON><PERSON> kalender", "calendar_label_repeat_monday_to_friday": "<PERSON><PERSON><PERSON> hari kerja (<PERSON><PERSON>)", "calendar_notifications": "Notif<PERSON><PERSON> kalender", "cancel_sync_google_calendar_success": "Sinkronisasi dengan kalender Google telah berhasil dibatalkan", "checking_calendars_with": "Memeriksa kalender dengan", "collab": {"active_meeting_button": "Aktifkan fungsi rapat", "back": "Kembali", "created_snackbar_message": "Rapat “%1s” berhasil dibuat", "discard": "Pembatalan diubah", "discard_editing_message": "<PERSON><PERSON>n yang Anda baru saja ubah akan hilang dan tidak dapat dipulihkan", "edited_snackbar_message": "Konten rapat “%1s” diubah", "unable_to_active_label": "Hanya kepala kelompok yang berhak mengaktifkan fungsi ini"}, "confirmDelete": {"accessDenied": "Anda tidak boleh menghapus %1s ini!", "cancel": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleteAppointment": "Hapus %1s?", "deleteAppointmentContent": "Anda yakin ingin menghapus %1s “%2s” ?", "deleteRecursiveTitle": "%1s ini dan semua %2s terkait", "deleteSuccess": "<PERSON><PERSON><PERSON><PERSON> menghapus %1s!"}, "confirm_to_unsync_google_calendar": "Anda yakin ingin membatalkan sinkronisasi Google kalender?", "create": {"add_host": "", "appbar_title": "Ciptakan rapat", "appbar_title_create_event": "Ciptakan rapat", "appbar_title_create_reminder": "Ciptakan pengingat jadwal", "appbar_title_create_todo": "Ciptakan p<PERSON>", "attachmentfiles": {"button_add": "Mengunggah", "title": "Lam<PERSON>ran", "title_add": "Tambah lampiran"}, "button_back_discard_event": "<PERSON><PERSON><PERSON>", "button_confirm_discard_event": "Membatalkan", "event_title_textfield_placeholder": "<PERSON><PERSON> rapat", "event_type": {"empty": "", "not_selected": "", "title": ""}, "label_all_day": "<PERSON><PERSON><PERSON> se<PERSON>jang hari", "label_attachment_file": "Lam<PERSON>ran", "label_edit_event_content": "Edit", "label_end": "<PERSON><PERSON><PERSON>", "label_event_content": "<PERSON><PERSON> rapat", "label_event_content_placeholder": "Klik untuk menambahkan catatan...", "label_meet": "Rapat online melalui Google Meet", "label_participants": "<PERSON><PERSON><PERSON>", "label_remind": "Mengingatkan", "label_repeat": "Men<PERSON><PERSON>", "label_save": "Menyimpan", "label_start": "<PERSON><PERSON>", "label_todo_content": "Konten untuk <PERSON>lak<PERSON>n", "label_zoom": "<PERSON><PERSON> rapat online", "message_discard_event": "%1s ini sedang dalam proses inisialisasi. <PERSON><PERSON>, semua informasi akan <PERSON>.", "not_google_signed_in": "<PERSON>a akan dinavigasi untuk masuk ke Google setelah membuat rapat untuk menyelesaikan inisialisasi", "not_grant_scope_google_description": "Mohon mengizinkan GapoWork memiliki akses penuh untuk membuat rapat", "not_grant_scope_google_drive_description": "Mohon mengizinkan GapoWork memiliki akses penuh untuk membuat rapat", "not_grant_scope_google_title": "<PERSON>a belum <PERSON><PERSON>n izin yang cukup", "placeholder_description_page": "Apa yang ingin Anda bagikan?", "popup_button_edit_event_back": "<PERSON><PERSON><PERSON>", "popup_button_edit_event_save": "Menyimpan", "popup_checkbox_title_edit_event": "Acara ini dan acara selan<PERSON>", "popup_description_edit_event": "<PERSON>a yakin ingin menyimpan per<PERSON>han acara", "popup_title_edit_event": "Ubah acara", "recursive_event_all": "Semua %1s", "recursive_event_only": "Hanya %1s ini", "recursive_event_this_next": "%1s ini dan %2s berik<PERSON>ny", "title_description_page": "<PERSON><PERSON>", "title_discard_event": "Hapus %1s ini?", "title_edit_event": "Ubah %1s"}, "currently_you_have_no_announcement": "Anda belum memiliki notifikasi apa pun", "deleted_event": {"close_btn": "", "description": "", "not_member_title": "", "title": ""}, "details": {"accept": "Ikut serta", "accept_bottomsheet_title": "Ikut serta dalam event", "appbar_title_event": "Rapat", "appbar_title_reminder": "<PERSON><PERSON><PERSON> jadwal", "appbar_title_todo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "approval_time": "", "attendee_list": "Daftar undangan", "button_add_people": "Menambahkan", "button_title_mark_as_completed": "Tandai selesai", "button_title_mark_as_uncompleted": "Tandai belum selesai", "confirm_title": "Konfirmasi partisipasi", "denied": "Tidak", "deny_bottomsheet_cancel": "Membatalkan", "deny_bottomsheet_confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deny_bottomsheet_title": "Tolak event", "email_title": "Undangan melalui email", "num_accepted": "Ikut serta", "num_attendee": "%1 anggota", "num_denied": "Menolak", "num_host": "", "participants": {"display_on_detail": "<PERSON><PERSON>ar undangan melalui email", "title": "Daftar undangan"}, "remind": {"day": "hari", "hour": "jam", "label": "Mengingatkan", "label_before_1_day_on": "Ingatkan 1 hari sebelumny pada", "label_on": "Ingatkan pada hari ini jam", "minute": "menit"}, "repeat": {"everyday": "<PERSON><PERSON>", "everymonth": "bulanan", "everyweek": "mingguan", "fifth": "<PERSON><PERSON>", "first": "pertama", "fourth": "<PERSON><PERSON>", "friday": "<PERSON><PERSON>", "friday_number": "", "label_daily": "<PERSON><PERSON><PERSON> set<PERSON> hari", "label_monthly": "<PERSON><PERSON><PERSON> set<PERSON> bulan", "label_weekday_with_week_position": "% mingguan % bulanan", "label_weekly": "<PERSON><PERSON><PERSON> setiap minggu", "label_yearly": "<PERSON><PERSON><PERSON> set<PERSON> tahun", "monday": "<PERSON><PERSON>", "monday1": "<PERSON><PERSON>", "mondayToFriday": "<PERSON><PERSON>", "monday_number": "", "saturday": "Sabtu", "saturday_number": "", "second": "<PERSON><PERSON>", "sixth": "<PERSON><PERSON>", "sunday": "<PERSON><PERSON>", "third": "<PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "thursday_number": "", "tuesday": "<PERSON><PERSON><PERSON>", "tuesday_number": "", "wednesday": "<PERSON><PERSON>", "wednesday_number": "", "weekday_with_week_position": "Ulangi pada % minggu % bulanan"}, "room": {"at": "Di"}, "room_approval": {"accept": "", "appbar_title": "", "approved": "", "approved_by": "", "approver": "", "cancel": "", "denied": "", "deny": "", "popup": {"accept_description": "", "accept_title": "", "deny_description": "", "deny_title": ""}, "snackbar": {"accept": "", "deny": ""}, "title": "", "waiting": ""}, "room_approval_title": "", "snackbar_add_attendee_success": "<PERSON><PERSON><PERSON><PERSON> peserta", "zoom": {"button_join": "<PERSON><PERSON><PERSON> ke ruang rapat online", "day_after_tomorrow": "lusa", "during_all_day_label": "<PERSON><PERSON><PERSON><PERSON> hari", "label_in_meeting": "Event sedang berlangsung", "label_meeting_is_ended": "Se<PERSON><PERSON>", "nua": "", "start_after": "<PERSON><PERSON>", "start_on": "<PERSON><PERSON> pada", "title": "<PERSON><PERSON> rapat online", "tomorrow": "besok"}}, "done": "Se<PERSON><PERSON>", "drag_drop": {"button_cancel": "Membatalkan", "button_send": "Mengirim", "label_notify_to_attendees_message": "<PERSON><PERSON> pember<PERSON>huan kepada para peserta?", "undo_title": "MEMBUKA", "update_success_message": "Pindah ke %."}, "email": {"discard_editing": "Batalkan pengeditan", "discard_editing_des": "Anda yakin ingin membatalkan pengeditan ini? data apa pun yang Anda masukkan tidak akan disimpan", "understood": "<PERSON><PERSON><PERSON><PERSON>", "wrong_email_format": "Format email salah", "wrong_email_format_des": "Mohon masukkan format email yang benar."}, "end": {"after": "Setela<PERSON>", "at": "<PERSON>da hari", "never": "Tidak pernah"}, "error": {"emptyTitle": "Konten tidak ada", "endTimeBeforeStartTime": "", "noPermissionAccess": "Atau tidak memiliki hak akses", "noPermissionAccessMsg": "Anda tidak dapat mengakses event ini", "openList": "<PERSON><PERSON> kalender <PERSON>"}, "eventMultipleAttendee": "Para peserta event", "filter_event": "FILTER EVENT", "gg_calendar": "Kelender Google", "google_drive": {"no_permission": {"accept": "<PERSON><PERSON> a<PERSON>", "cancel": "<PERSON><PERSON>", "content": "<PERSON><PERSON><PERSON> minta akses melalui akun Google Anda.", "title": "Anda tidak memiliki hak untuk mengakses lampiran"}}, "host": "", "hosts": "", "label_add_zoom_room": "Tambahkan ruang rapat untuk event", "label_all_day": "Sepanjang hari", "label_all_day_2": "Ini adalah %1s sepanjang hari", "label_list_invitees": "Daftar undangan untuk berpartisipasi", "label_no_remind_before": "Tidak diingatkan sebelumnya", "label_no_repeat": "Tidak <PERSON>", "label_remind_before_days": "Ingatkan % hari sebelumnya", "label_remind_before_days_on": "Ingatkan % hari pada % AM", "label_remind_before_hours": "Ingatkan % jam", "label_remind_before_minutes": "Ingatkan % menit", "label_remind_this_day_on": "Ingatkan pada tanggal ini pada % AM", "label_repeat_custom": "<PERSON><PERSON><PERSON><PERSON>....", "label_repeat_custom_title": "Kusto<PERSON><PERSON> be<PERSON>", "label_repeat_daily": "<PERSON><PERSON><PERSON> set<PERSON> hari", "label_repeat_end": "Se<PERSON><PERSON>", "label_repeat_every": "<PERSON><PERSON><PERSON>", "label_repeat_monthly": "<PERSON><PERSON><PERSON> set<PERSON> bulan", "label_repeat_weekly": "<PERSON><PERSON><PERSON> setiap minggu", "label_title_event": "Event", "label_zoom": "Menambahkan ruang rapat untuk acara", "longPress": {"deleteAppointment": "Hapus %1s ini", "edit": "Mengedit event", "editAppointment": "Mengedit %1s ini", "edit_appointment_bottom_sheet_title": "Mengedit %1s", "markComplete": "Tandai selesai"}, "meeting_room": "<PERSON><PERSON> rapat", "ms_outlook": "Kalender MS Outlook", "nameTag": "<PERSON><PERSON> merek", "notification_title": "<PERSON><PERSON><PERSON> kalender", "other_people": "orang yang lain", "refreshSuccess": "<PERSON><PERSON><PERSON> telah <PERSON>", "repeat": {"by_day": "<PERSON>", "by_month": "<PERSON><PERSON><PERSON>", "by_week": "<PERSON><PERSON><PERSON>", "by_year": "<PERSON><PERSON>"}, "repeat_by_month_on_a_day": "Pada tanggal %1s setiap bulan", "repeat_by_month_on_a_weekday": "Pada %1s minggu %2s bulanan", "repeat_by_month_on_last_weekday": "Pada %1 terakhir setiap bulan", "repeat_by_year_on_a_day": "Pada %1s set<PERSON>p tahun", "repeat_by_year_on_a_weekday": "Pada %1s minggu %2s %3s set<PERSON>p tahun", "repeat_display": {"after": "<PERSON><PERSON><PERSON> %1s kali", "daily": "Ulangi %1s sekali sehari", "daily_once": "<PERSON><PERSON><PERSON> set<PERSON> hari", "end_at": "sampai %1s", "monthly_on_a_day": "Ulangi %1s sebulan sekali pada tanggal %2s", "monthly_on_a_day_once": "Ulangi pada hari %1 terakhir setiap bulan", "monthly_on_a_weekday": "Ulangi %1s sebulan sekali pada tanggal %2s minggu %3s set<PERSON>p bulan", "monthly_on_a_weekday_once": "<PERSON>lang<PERSON> setiap bulan pada tanggal %1s minggu %2s bulanan", "monthly_on_a_weekday_weekOrdinal": "", "monthly_on_last_weekday": "<PERSON><PERSON><PERSON> setiap bulan sekali pada tanggal %2s te<PERSON><PERSON> setiap bulan", "monthly_on_last_weekday_once": "Ulangi setiap bulan pada tanggal %1 terakhir setiap bulan", "repeat_on_day_of_week": "<PERSON><PERSON>i pada", "weekly": "Ulangi %1s seminggu sekali pada %2s", "weekly_once": "Ulangi %1s seminggu sekali", "weekly_once_on_weekday": "Ulangi setiap minggu pada %1s", "yearly": "Ulangi %1s tahun sekali", "yearly_on_a_day": "Ulangi %1s tahun sekali pada tanggal %2s %3s", "yearly_on_a_day_once": "Ulangi setiap tahun pada %1s %2s", "yearly_on_a_weekday": "Ulangi %1s setahun sekali pada %2s minggu %3s dari %4s", "yearly_on_a_weekday_once": "Ulangi setiap tahun pada %1s minggu %2s dari %3s"}, "room": {"add": "Tambah", "all_area": "<PERSON><PERSON><PERSON>", "all_room_busy_all_day_event_title": "<PERSON><PERSON><PERSON> ruang rapat sibuk", "all_room_busy_description": "<PERSON><PERSON><PERSON> pilih waktu lain untuk memesan ruang rapat", "all_room_busy_title": "<PERSON><PERSON><PERSON> ruang rapat sibuk selama jam %1 - %2", "approver": "", "area": "Area", "available": "<PERSON><PERSON>", "busy": "Sibuk", "edit_time_room_busy_description": "<PERSON><PERSON><PERSON> pilih waktu lain untuk memesan kembali ruang rapat", "edit_time_room_busy_title": "Ruang rapat sibuk selama jam %1 - %2", "empty_area": "Tidak ada area yang tersedia", "empty_room": "Tidak ada ruang rapat yang tersedia", "empty_room_search": "Tidak ada hasil", "meeting_room": "<PERSON><PERSON> rapat", "no_room_description": "Organisasi Anda belum memiliki ruang rapat di GapoWork.", "no_room_selected": "Tidak ada ruang rapat yang dipilih", "no_room_title": "Belum ada ruang rapat yang dibuat", "search_area_hint": "Mencari area", "warning_message": "<PERSON><PERSON> \"berulang\" tidak tersedia saat memesan ruang rapat", "warning_message_multi_day": ""}, "room_tab": {"booking_room_tab": "<PERSON><PERSON> rapat", "busy_room": "<PERSON><PERSON> rapat sibuk", "busy_room_snackbar": "<PERSON><PERSON> rapat telah dipesan untuk waktu yang Anda pilih. <PERSON><PERSON><PERSON> pilih waktu lain", "calendar_tab": "<PERSON><PERSON><PERSON>a", "change_room": "Ganti", "not_have_any_room_description": "Organisasi Anda belum memiliki ruang rapat di GapoWork.", "not_have_any_room_title": "Belum ada ruang rapat yang dibuat", "search_hint": "<PERSON><PERSON>"}, "select_invitees": {"button_continue": "<PERSON><PERSON><PERSON><PERSON>", "button_members_list_detail": "Detail anggota", "button_more_members_detail": "<PERSON><PERSON> lebih banyak anggota", "button_select_all": "<PERSON><PERSON><PERSON>", "button_title_view_attendees": "Lihat %1s yang bergabung", "group_cell_member": "anggota", "is_added_by_addmin_str": "Ditambahkan oleh <PERSON>", "pick_all_department_title": "<PERSON><PERSON><PERSON> depart<PERSON>", "placeholder_search": "<PERSON><PERSON>", "search_organization": "", "tab_title_bot": "<PERSON><PERSON><PERSON>", "tab_title_department": "Departemen", "tab_title_group": "Grup obrolan", "tab_title_member": "Anggota", "tab_title_role": "Jabatan", "tab_title_workspace": "", "title_invite": "Undang untuk bergabung"}, "separator_________________________": "--------------------------", "snack_bar_message_create_event_successfully": "<PERSON><PERSON>a ber<PERSON>il dibuat", "sync": {"google_grant_scope": {"action": "Berikan akun Google", "message": "Untuk menyinkronkan data kalender dari Google Calendar dan <PERSON>, silahkan berikan izin terkait Google Calendar", "title": "Silahkan berikan izin Calendar"}, "google_not_sync_event": "", "google_not_sync_event_detail_tooltip": "", "google_not_sync_event_tooltip": "", "google_not_sync_header": "", "google_sync_button": "", "google_title_bottom_sheet_sync_again": "Sinkronkan ulang Google?", "google_title_sync_expired": "Batas waktu sinkronisasi dengan Google sudah habis", "outlook_bottom_sheet_filter_button_gapowork_title": "GapoWork", "outlook_bottom_sheet_filter_button_google_title": "Kalender <PERSON>", "outlook_bottom_sheet_filter_button_outlook_title": "Kalender MS Outlook", "outlook_bottom_sheet_filter_section_title": "KALENDER SAYA", "outlook_button_bottom_sheet_sync_again": "Sinkronkan lagi", "outlook_button_bottom_sheet_unsync": "Batalkan sink<PERSON>", "outlook_description_bottom_sheet_sync_again": "<PERSON>as waktu sinkron<PERSON>si sudah habis, <PERSON>a perlu menyinkronkan ulang untuk terus menggunakannya.", "outlook_description_bottom_sheet_sync_expired": "Batas waktu sinkronisasi sudah habis", "outlook_description_bottom_sheet_unsync": "Anda yakin ingin membatalkan sinkronisasi kalender MS Outlook?", "outlook_sync_button": "", "outlook_title_bottom_sheet_button_sync": "Sinkronkan dengan kalender MS Outlook", "outlook_title_bottom_sheet_section": "AKUN KALENDER MS OUTLOOK", "outlook_title_bottom_sheet_sync_again": "Sinkronkan ulang MS Outlook?", "outlook_title_bottom_sheet_unsync": "Batalkan sink<PERSON>", "outlook_title_button_bottom_sheet_later": "<PERSON><PERSON>", "outlook_title_button_bottom_sheet_unsync": "Batalkan sink<PERSON>", "outlook_title_button_sync_now": "SINKRONKAN SEKARANG", "outlook_title_snackbar_unsync": "Berhasil membatalkan sinkronisasi dengan kalender MS Outlook", "outlook_title_sync_expired": "Sinkronkan dengan kalender MS Outlook yang batas waktunya sudah habis", "outlook_title_synced": "<PERSON><PERSON><PERSON> be<PERSON><PERSON> disin<PERSON>", "outlook_title_syncing": "Sedang menyinkronkan dengan kalender MS Outlook", "outlook_title_unsynced": "Sinkronisasi kalender MS Outlook berhasil dibatalkan", "remove_google_event": "", "section_title": "", "sync_now": ""}, "sync_google_calendar_success": "<PERSON><PERSON><PERSON> be<PERSON><PERSON> disin<PERSON>", "syncing_with_google_calendar": "Sinkronisasi dengan kalender Google", "textfield_description_placeholder": "Deskripsi untuk %1s ini", "textfield_title_placeholder": "Tambahkan judul untuk acara ini", "textfield_title_placeholder_reminder": "Tambahkan judul untuk pengingat kalender", "textfield_title_placeholder_todo": "Tambahkan judul untuk tugas", "time_disappear": "<PERSON><PERSON><PERSON>", "title_attendees": "<PERSON><PERSON><PERSON>", "type": {"meeting": "<PERSON><PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON><PERSON>", "reminder": "Pengingat kalender", "title_meeting": "<PERSON><PERSON><PERSON><PERSON>", "title_other": "<PERSON><PERSON><PERSON>", "title_reminder": "Acara pengingat", "title_todo": "<PERSON> yang harus dilakukan", "todo": "<PERSON><PERSON>"}, "unsync_google_calendar": "Batalkan sink<PERSON>", "until": "<PERSON><PERSON><PERSON>", "usync_button_title": "Batalkan sink<PERSON>", "will_be_displayed_here": "akan di<PERSON>pi<PERSON>an di sini.", "you": "<PERSON><PERSON>"}, "coin": {"bottom_navigation_bar_gift": "", "bottom_navigation_bar_my_wallet": "", "bottom_navigation_bar_qr_code": "", "deliver_person": "", "gift_list": {"empty_description": ""}, "gift_transaction": {"redeem_gift": "", "redeem_gift_canceled": "", "refund_point": ""}, "qr": {"move_to_frame_to_capture": "", "qr_code_accept": "", "qr_code_canceled": "", "qr_code_expired": "", "qr_code_name_title": "", "qr_code_not_detected": "", "qr_code_not_effective": "", "qr_code_not_found": "", "qr_code_scan_reach_limit": "", "qr_code_user_reach_limit": "", "qr_coin_amount_received_title": "", "qr_coin_amount_subtraction_title": "", "qr_coin_successfully_received": "", "qr_coin_successfully_subtracted": ""}, "receiver": "", "scan_qr_title": "", "select_photo": "", "trading": {"detail": {"reason": {"title": ""}, "receive": {"title": ""}, "status": {"failed": "", "processing": "", "success": ""}, "time": "", "title": "", "transaction": {"is_not_recall_point": "", "recall_point": "", "type": ""}, "transfer": {"organization": "", "title": ""}, "value": {"title": ""}}, "history": {"current_balance": "", "empty": {"content": "", "title": ""}, "item": {"because": "", "in": {"reason": "", "title": ""}, "out": {"reason": "", "title": ""}}}}, "understood": ""}, "conversations_title": "Mengobrol", "detail_text": "", "drive": {"appbar": {"search": "", "title": {"bin": "", "default": "", "my": "", "search": "", "share": ""}}, "assignee_picker": {"action_done": {"logs_permission": "", "share_files": ""}, "title_pick": {"logs_permission": "", "share_files": ""}}, "behavior": {"add": {"file": "", "folder": "", "image_or_video": "", "url": ""}, "item": {"back_to_home": "", "copy_link": "", "create_shortcut": "", "delete": "", "delete_permanent": "", "details": "", "download": "", "move": "", "preview": "", "rename": "", "restore": "", "share": ""}, "move": {"button_done": "", "search_empty_content": "", "search_empty_title": "", "search_hint": "", "tab": {"my": "", "shared": ""}, "title": ""}, "roll_back": ""}, "bin": {"delete_all": "", "hint": ""}, "empty": {"bin_drive": {"content": "", "title": ""}, "my_drive": {"content": "", "title": ""}, "search": {"appbar": {"title": ""}, "content": "", "title": ""}, "share_drive": {"content": "", "title": ""}}, "file_management": {"common": {"default_folder_name": "", "file": "", "file_hint": "", "handling": "", "last_updated_at": "", "success": ""}, "download": {"cancel": "", "empty_data": "", "title": ""}, "rename": {"folder": ""}, "upload": {"cancel": "", "empty_data": "", "title": ""}}, "file_type": {"folder": "", "photo": "", "video": ""}, "logs": {"activity": {"filters": {"all": "", "history": "", "permission_changed": ""}}, "details": {"created_date": "", "file_type": "", "headers": {"info": "", "user_permission": ""}, "info": "", "last_modified_date": "", "member": "", "owner": "", "share_link": "", "share_link_content": ""}, "tab": {"activity": "", "details": ""}}, "popup": {"cancel_upload": {"cancel": "", "confirm": "", "content": "", "title": ""}, "create_folder": {"action": "", "input_hint": "", "input_name": "", "title": ""}, "delete_all": {"cancel": "", "confirm": "", "content": "", "title": ""}, "delete_item_bin": {"permanent_delete": "", "recovery": ""}, "delete_permanent": {"cancel": "", "confirm": "", "content": "", "title": ""}, "delete_share": {"cancel": "", "confirm": "", "file_content": "", "file_title": "", "folder_content": "", "folder_title": ""}, "file_not_exists": {"close": "", "content": "", "title": ""}, "move": {"cancel": "", "confirm": "", "content": "", "title": ""}, "rename_folder": {"action": "", "title": ""}, "restore": {"admin_content": "", "cancel": "", "close": "", "confirm": "", "file_content": "", "file_not_exits_content": "", "title": ""}, "share_add_member": {"cancel": "", "confirm": "", "content": "", "title": ""}, "snack_bar": {"copy_link": "", "delete": "", "delete_all": "", "delete_permanent": "", "download": "", "error": {"delete": "", "delete_all": "", "move": "", "recovery": ""}, "invite": "", "move": "", "recovery": {"call_to_action": "", "leading": "", "tailing": ""}, "rename": "", "share": "", "share_edited": "", "transfer_owner": ""}}, "search": {"hint": ""}, "share": {"appbar": {"title": {"add_member": "", "done": "", "invite_member": "", "share": ""}}, "btn": {"add_member": "", "copy_link": "", "invite": "", "save": ""}, "common_permission": {"header": "", "restrict": "", "restrict_hint": "", "workspace_editor": "", "workspace_editor_hint": "", "workspace_viewer": "", "workspace_viewer_hint": ""}, "file_access": {"full": {"delete": "", "editor": "", "owner": "", "viewer": ""}, "short": {"editor": "", "owner": "", "viewer": ""}, "title": ""}, "headers": {"common_permission": "", "member_can_access": ""}, "in_workspace": "", "in_workspace_hint": "", "restrict": "", "restrict_hint": "", "send_noti": {"checkbox_send_title": "", "hint": ""}}, "tab": {"bin_drive": "", "my_drive": "", "share_drive": ""}}, "error": {"cantOpenFile": "", "default_msg": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>, silahkan coba lagi!", "empty": "Tidak ada data", "error": {"400101": "", "400102": "", "empty": "", "need_atleast_one_row": "", "no_precise_location": {"description": "Turn on \"Precise Location\" to get precise location", "title": "Allow access precise location"}}, "message_search_empty": "<PERSON><PERSON>, tidak ada hasil pencarian yang cocok untuk ditampilkan.", "noAppToOpen": "", "nodata": "Tidak ada hasil yang cocok ditemukan", "title_search_empty": "Tidak ada hasil yang cocok", "unable_load_page_des": "<PERSON><PERSON><PERSON> kes<PERSON>han telah terjadi. <PERSON><PERSON>an coba lagi atau periksa kembali koneksi internet Anda.", "unable_load_page_title": "Tidak dapat memuat halaman", "wifi_connection": "Tidak ada koneksi internet, silahkan periksa koneksi Anda!"}, "error_mapper": {"cancellation": "", "decode_error": "", "network": "", "parse": {"error_mapping": "", "invalid_format": "", "invalid_source_format": ""}, "server_defined": {"E400": "", "E401": "", "E404": "", "E500": "", "E502": "", "E503": "", "E504": ""}, "server_undefined": "", "timeout": "", "uncaught": "", "unknow": "", "validation": {"empty": "", "invalid_data": "", "invalid_data_type": ""}}, "feature_info": {"can_not_download_file": ""}, "gift_detail": {"cancel_reason": "", "delivery_address": {"address_hint": "", "address_label": "", "address_placeholder": "", "confirm_button": "", "title": ""}, "description": "", "exchange_gift": "", "exchange_success": "", "gift_detail_exchange_success": "", "gift_detail_out_of_stock": "", "gift_name": "", "images": "", "quantity": "", "remainning": "", "required_points": ""}, "gift_exchange": {"confirm_received": "", "confirm_received_success": "", "receive_point": "", "redeem_point_reason": ""}, "gift_exchange_status": {"canceled": "", "failed": "", "pending_approval": "", "received": "", "waiting_receive": ""}, "loading": "<PERSON>gi memuat", "media_preview": {"could_not_prevew": "Format tidak dapat dipratinjau", "download": "<PERSON><PERSON><PERSON>", "download_sucessfully": "<PERSON><PERSON><PERSON> dengan sukses", "download_unsucessfully": "<PERSON><PERSON><PERSON> gagal", "downloading": "<PERSON><PERSON>", "open": "Membuka"}, "memberPicker": {"assignee_search_text": "<PERSON><PERSON><PERSON> dengan nama atau email", "limit_user_description": "Anda telah memilih yang melebihi jumlah anggota yang dapat dikreditkan", "limit_user_title": "Maksimum % anggota"}, "mini_app": {"emty_view": ""}, "notification": {"addAssignee": "% sudah tambahkan pelaksana %", "addTime": "Tambahkan waktu", "addWatcher": "% sudah tambahkan pengikut %", "calendarChildNoti": {"attachments": "", "description": "", "hasMeeting": "", "inEvent": "", "owner": "", "remindBefore": "", "room": "", "scheduleType": "", "time": "", "title": ""}, "changedStatus": "% sudah mengubah status tugas", "commented": "% sudah berkomentar", "commentedToATask": "% mengomentari tugas", "deletedTask": "% sudah hapus tugas", "edittedTask": "% sudah mengupdate tugas", "emptyTitle": "Anda belum memiliki notifikasi apa pun", "endDate": "<PERSON><PERSON><PERSON>", "isNearlyExpired": "Tugas akan habis waktunya dalam {count} lagi", "isOverDueDate": "Tugas sudah habis waktunya", "mentionedYouInAComment": "% menyebutkan Anda dalam suatu tugas", "projectIsHidden": "", "snackbar_read_all_noti": "Tandai semua telah dibaca", "startDate": "<PERSON><PERSON>", "updatedDueDate": "% telah mengubah batas waktu", "viewAllTasks": "<PERSON><PERSON> semua tugas"}, "orgChart": "Struktur organisasi", "portal": {"mini_app_title": "", "portal_add_link": "", "portal_add_link_done": "", "portal_add_link_error": "", "portal_add_link_error_empty": "", "portal_add_link_error_empty_url": "", "portal_add_link_placeholder": "", "portal_add_link_title": "", "portal_add_link_title_placeholder": "", "portal_add_link_url": "", "portal_add_onback_bottom_confirm_content": "", "portal_add_onback_bottom_confirm_title": "", "portal_add_title": "", "portal_apply": "", "portal_cancel": "", "portal_delete_link": "", "portal_delete_link_confirm": "", "portal_delete_link_done": "", "portal_edit_link": "", "portal_edit_link_done": "", "portal_empty_title": "", "portal_problem_with_server": "", "portal_problem_with_server_close": "", "portal_problem_with_server_description": "", "portal_succes": "", "portal_title": "", "portal_user_empty_view": "", "portal_warning_delete_description": "", "portal_warning_maximum_close": "", "portal_warning_maximum_description": "", "portal_warning_maximum_title": ""}, "reload_page": "<PERSON><PERSON> ulang halaman", "task": {"activity": "Aktivitas", "addDueDate": "Le<PERSON>h banyak batas waktu", "addMoreAssignee": "<PERSON><PERSON><PERSON> banyak pelaksana", "addPriority": "Tambahkan prioritas", "addTag": "Tambahkan label", "addWatcher": "Tambahkan pengikut", "adminCreateLimitDescription": "<PERSON>a telah kehabisan batas fitur ini. <PERSON><PERSON><PERSON> aks<PERSON> GapoWork di situs web untuk melakukan upgrade.", "all": "<PERSON><PERSON><PERSON>", "allTag": "<PERSON><PERSON><PERSON> stiker", "archive": "Penyimpanan tugas", "archived": "Penyimpanan", "areYouSureDeleteTag": "Anda yakin ingin menghapus stiker dari proyek ini?", "assignedByMe": "Dibuat oleh saya", "assignedToMe": "<PERSON>rikan kepada saya", "assignee": {"add": "<PERSON><PERSON>", "assignMe": "<PERSON><PERSON>", "assignees": "<PERSON><PERSON><PERSON><PERSON>", "changSubtaskToTask": "Ubah ke tugas", "empty_assignee": "Belum ada yang ditambahkan ke dalam daftar", "follower": "<PERSON><PERSON><PERSON><PERSON>", "list": "<PERSON><PERSON><PERSON>", "list_filter": "<PERSON><PERSON><PERSON> p<PERSON>", "member": "Melakukan", "remove_filter": "Tanpa filter", "save": "Menyimpan", "select": "<PERSON><PERSON><PERSON>", "select_member": "<PERSON><PERSON><PERSON> orang"}, "attachFile": "Lam<PERSON>ran", "attachFileCamera": "Mengambil foto", "attachFileChooseFile": "Pilih file", "attachFileChooseImage": "<PERSON><PERSON><PERSON> foto", "attachFileChooseMedia": "Pilih foto/video", "attachFileChooseVideo": "<PERSON><PERSON><PERSON> video", "attachFileDownload": "Unduh file", "attachFileRemove": "Hapus file", "attachFileRemoveButtonTitle": "<PERSON><PERSON><PERSON>", "attachFileRemoveMessage": "Penghapusan tidak dapat diurungkan. A<PERSON><PERSON>h Anda yakin ingin menghapus file ini?", "attachFileScreenTitle": "Lam<PERSON>ran", "attachFileUpload": "Mengunggah", "bottom_sheet_item_to_task_list": "<PERSON><PERSON> daftar tugas", "cant_find_task": "Tidak ada peker<PERSON>an yang di<PERSON>n", "cant_performed_archived_task": "Tugas ini tidak dapat dilakukan dengan pekerjaan tersimpan", "changeSubTaskToTask": "Ubah ke tugas", "changed": "Ubah", "changedJob": "ke suatu tugas", "commentHere": "<PERSON><PERSON> k<PERSON>", "commentUploadError": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat mengunggah", "commentUploadTryAgain": "Mencoba kembali", "confirmCancelUploading": {"message": "<PERSON><PERSON> Anda keluar dari layar ini, unggahan akan dibat<PERSON>an. <PERSON>lahkan tunggu hingga unggahan selesai.", "no": "<PERSON><PERSON><PERSON><PERSON>", "title": "Tunggu sebentar!", "yes": "Bat<PERSON><PERSON>"}, "confirmDeleteComment": "Anda yakin ingin menghapus komentar ini?", "confirmDelete_cancel": "<PERSON><PERSON><PERSON>", "confirmDelete_title": "<PERSON><PERSON><PERSON><PERSON>", "contentDeleteDependencies": "Tugas ini tergantung pada peker<PERSON>an lain. <PERSON><PERSON><PERSON>h Anda yakin ingin menghapus tugas ini?", "contentDeleteTaskDependency": "Tugas ini tergantung pada peker<PERSON>an lain. <PERSON><PERSON><PERSON>h Anda yakin ingin menghapus tugas ini?", "contentDuplicateTaskDependency": "Tugas ini dipasang sebagai dependensi sehingga tidak dapat dipindahkan ke proyek lain. GapoWork akan membuat salinan tugas untuk Anda.", "contentLogOutTagWhenBack": "<PERSON><PERSON>, konten yang <PERSON>a buat akan di<PERSON>.", "contentYouSureExitEditTag": "<PERSON><PERSON>, pengeditan <PERSON> akan <PERSON>.", "copiedCommentContent": "Komentar disalin", "copiedTaskUrl": "<PERSON>tan disalin", "copyComment": "<PERSON><PERSON> komentar", "copyOf": "Salinan dari", "createLimitTitle": "Tidak dapat membuat lebih banyak", "createNewTask": "Buat tugas baru", "createTag": "<PERSON><PERSON><PERSON> stiker", "createTask": "Membuat", "createTodoDone": "Se<PERSON><PERSON>", "created": "telah dibuat", "datetimePicker": {"add": "Tambah", "alertTimeInPast": "<PERSON><PERSON><PERSON> tidak valid", "alertTimeInPastContent": "Waktu %1s tidak boleh kurang dari waktu saat ini!", "cancel": "Membatalkan", "choose": "<PERSON><PERSON><PERSON>", "datePickerTitle": "<PERSON><PERSON><PERSON><PERSON> jadwal", "done": "Se<PERSON><PERSON>", "duplicateCreateTime": "", "duplicateDate": "", "duplicateDueDate": "", "duplicateTaskInclude": "Salinan tugas term<PERSON>", "duplicateTime": "", "enterDate": "<PERSON><PERSON><PERSON><PERSON> tanggal", "enterTime": "<PERSON><PERSON><PERSON><PERSON> waktu", "from": "dari", "onMonthDay": "<PERSON>da tanggal", "onWeekDay": "<PERSON>da hari", "pickDate": "<PERSON><PERSON><PERSON> tanggal", "repeat": "Men<PERSON><PERSON>", "repeatEvery": "<PERSON><PERSON><PERSON>", "repeatEveryYear": "<PERSON><PERSON><PERSON> set<PERSON> tahun", "repeatTime": "<PERSON><PERSON>i waktu", "taskRepeat": "<PERSON><PERSON><PERSON> tugas", "timePickerTitle": "<PERSON><PERSON><PERSON> waktu", "watcher": "Pengikut"}, "deleteComment": "<PERSON><PERSON> komentar", "deleteDependencies": "<PERSON><PERSON> tugas yang bergantung", "deleteTag": "<PERSON><PERSON> stiker", "dependencies": "<PERSON><PERSON> yang bergantung", "descriptionHint": "Deskripsi tugas...", "detail": "Detail", "dueDate": "<PERSON><PERSON> waktu", "dueDate403": "", "editComment": "Mengedit komentar", "editTag": "<PERSON><PERSON><PERSON> stiker", "editedTagSuccess": "<PERSON><PERSON><PERSON> <PERSON><PERSON> diedit", "exit": "<PERSON><PERSON><PERSON>", "files": "Dokumen", "filterTag": "Stiker yang difilter", "filterTitle": "Penyaring tugas", "filter_by_assignee": "<PERSON><PERSON><PERSON><PERSON> be<PERSON> pela<PERSON>ana", "folderMoved": "Detail", "hideProject": "Sembunyikan proyek", "ignoreActivityLogTitle": "Detil", "in": "", "in_section": "Bagian", "inputNameTag": "Masukkan nama label", "listTag": "<PERSON><PERSON><PERSON> stiker", "location": "<PERSON><PERSON>", "longPress": {"accessDenied": "Anda tidak diizinkan untuk menghapus tugas ini!", "clone": "<PERSON><PERSON>t salinan", "confirmDeleting": "<PERSON><PERSON><PERSON> konten dan aktiv<PERSON> tug<PERSON>, set<PERSON><PERSON>, tidak dapat di<PERSON>.\n<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus tugas ini?", "copied": "Tautan sudah disalin", "copyUrl": "<PERSON><PERSON>an", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleteTask": "<PERSON><PERSON>", "deleted": "Tugas berhasil di<PERSON>pus!", "edit": "<PERSON><PERSON><PERSON> tugas", "subTaskDeleted": "Sub-tugas berhasil di<PERSON>!", "view": "Lihat detail"}, "manageTag": "<PERSON><PERSON><PERSON> stiker", "mediaPicker": {"errorLimitContent": "Anda hanya dapat memilih maksimal 1 foto/video", "errorLimitTitle": "Melebihi jumlah yang diperbolehkan"}, "moveFolder": "<PERSON><PERSON> saya", "moveTaskList": "Tugas selanju<PERSON>", "my_task": "Tugasku", "nextTasks": "Tugas selanju<PERSON>", "notificationSettings": "<PERSON><PERSON><PERSON> tugas", "notificationTitle": "Pemberitahuan tugas", "openTaskList": "<PERSON><PERSON> daftar tugas", "pickAssigneeDone": "Se<PERSON><PERSON>", "pickAssigneeTitle": "Lebih banyak orang", "pickAssignees": "<PERSON><PERSON><PERSON>", "pick_color": "<PERSON><PERSON><PERSON> warna", "pickdueDate": "<PERSON><PERSON><PERSON> batas waktu", "previousTasks": "<PERSON><PERSON> per<PERSON>", "priority": {"high": "Prioritas utama", "low": "Prioritas rendah", "medium": "Prioritas sedang", "none": "Tidak ada prioritas", "short": {"high": "Prioritas utama", "low": "Rendah", "medium": "Sedang", "none": "Tidak"}}, "projectNotTag": "Proyek belum memiliki stiker.", "projectNotTagPlease": "Stiker ini belum tersedia", "projectPickerTitle": "Pilih lokasi untuk menyimpan tugas", "projectSearchHint": "<PERSON><PERSON><PERSON>, folder atau daftar...", "projectSectionPickerTitle": "<PERSON><PERSON>", "readMore": "lihat lebih banyak", "removeTag": "<PERSON><PERSON> stiker", "reply": "<PERSON><PERSON><PERSON>", "replyComment": "Balas ke komentar", "save": "Menyimpan", "saveEditTag": "Si<PERSON>an edit", "search": "<PERSON><PERSON><PERSON>", "search_tag": "<PERSON><PERSON> stiker", "section": {"confirmDiscard": {"description": "Tugas ini sedang berlangsung. <PERSON><PERSON>, semua informasi akan di<PERSON>.", "discard": "Membatalkan", "later": "<PERSON><PERSON>", "title": "Batalkan tugas ini?"}, "confirmRemove": {"cancel": "Membatalkan", "keepTask": "Hapus item ini dan mempertahankan %1 tugas", "remove": "Hapus item", "removeSectionAnd": "Hapus item ini dan %1s tugas", "title": "Anda yakin ingin menghapus item ini?"}, "create": {"pickColor": "<PERSON><PERSON>h warna untuk item baru", "setName": "Beri nama item ini..."}, "createNewSection": "Buat item baru", "createdSuccessfully": "Item baru ber<PERSON>il dibuat", "more": {"edit": "Ganti nama item", "remove": "Hapus item"}, "move_to_section": "Tugas sudah dipindahkan ke item", "removeAllSuccessfully": "Item dan tugas di dalam sudah dihapus", "removeSuccessfully": "<PERSON>em ber<PERSON>", "titleAction": {"create": "Membuat", "save": "Menyimpan"}, "updateSuccessfully": "Nama item telah diubah"}, "selectPosition": "<PERSON><PERSON><PERSON> posisi tugas", "selectPositionItem": "<PERSON><PERSON><PERSON> posisi tugas", "selectTag": "<PERSON><PERSON><PERSON> stiker", "showAsAProject": "Tam<PERSON>lkan sebagai sebuah proyek", "showLess": "<PERSON><PERSON><PERSON><PERSON> lebih sedikit", "snackBarAttachFile": "File", "snackBarEmptyTodoTitle": "<PERSON><PERSON><PERSON> masukkan nama tugas", "snackBarRemoved": "te<PERSON>", "sort": {"byCreatedAt": "Mengatur berdasarkan waktu pembuatan", "byDueDate": "<PERSON><PERSON><PERSON> berdasarkan batas waktu", "byDueDateAsc": "", "byDueDateDesc": "", "byPriority": "Men<PERSON>ur berdasarkan prioritas", "sort": "<PERSON><PERSON><PERSON>"}, "status": {"all": "<PERSON><PERSON><PERSON>", "done": "Se<PERSON><PERSON>", "haveChangedTo": "sudah be<PERSON>h menjadi", "inprogress": "Sedang berl<PERSON>g", "status": "Status", "title": "Status tugas", "todo": "<PERSON><PERSON>"}, "status_v2": {"completed": "Se<PERSON><PERSON>", "markComplete": "Se<PERSON><PERSON>"}, "tag": "Stiker", "tagHaveNotBeenProject": "Stiker ini telah ditambahkan ke proyek", "tagPicked": "<PERSON><PERSON>r yang dipilih", "tagRemoveSuccess": "<PERSON><PERSON><PERSON> te<PERSON> be<PERSON><PERSON>", "task": "TUGAS", "taskContent": "konten tugas", "taskContentSave": "Menyimpan", "taskEmpty": "Konten tidak ada", "taskListMoved": "Daftar tugas dipinda<PERSON>kan", "taskListSearchHint": "<PERSON><PERSON> daftar tugas", "taskNoAccess": "Atau tidak memiliki izin untuk mengakses", "taskSaveCopy": "Menyimpan", "taskSearchPlaceHolder": "<PERSON>i be<PERSON> nama tugas", "taskSearchTitle": "<PERSON><PERSON><PERSON> tugas", "taskSelected": "Tugas yang dipilih", "taskTitle": "<PERSON><PERSON> saya", "task_lowercase": "Tugas", "task_notifications": "pen<PERSON><PERSON> tugas", "task_of": "<PERSON><PERSON> dari", "task_subTask": "Tugas sampingan", "thisProjectIsNotShownInTaskManagement": "Proyek ini tidak ditampilkan dalam manajemen tugas", "thisProjectIsShownInTaskManagement": "Proyek ini ditampilkan dalam manajemen tugas", "titleHint": "<PERSON><PERSON>kkan nama tugas ...", "todo": "<PERSON><PERSON> tambahan", "unarchive": "Pulih<PERSON> tugas", "understand": "<PERSON>dah paham", "uploadFileSizeLimitContent": "File foto terlalu besar, silah<PERSON> pilih foto di bawah 10 MB", "userCreateLimitDescription": "<PERSON>a telah kehabisan batas fitur ini. <PERSON><PERSON><PERSON> hubungi Administrator.", "viewMoreComments": "Lihat lebih banyak %1s komentar", "viewMoreReplies": "%1s respons", "watching": "<PERSON><PERSON><PERSON><PERSON>", "whenMovedToAnotherProjectAllTasks": "Saat dipinda<PERSON>kan ke proyek lain, semua tugas akan kehilangan informasi: “<PERSON><PERSON><PERSON>, Pengamat, Tag”"}, "taskCollab": {"addTask": "Tambahkan tugas", "alertDes": "Fungsi ini akan diterapkan ke semua anggota grup obrolan dan tidak dapat diurungkan. Apakah Anda ingin mengaktifkan?", "cancel": "Membatalkan", "empty": {"actionTitle": "<PERSON>uat daftar tugas", "desciption": "Folder dan daftar tugas yang Anda buat akan muncul di sini.", "title": "Belum ada daftar tugas"}, "enable": "Aktifkan", "enableCollabButton": "Aktifkan fungsi tugas", "enableCollabDes": "<PERSON><PERSON><PERSON> tugas dengan mudah bersama anggota tim <PERSON>", "enableCollabTitle": "<PERSON><PERSON> dalam tim <PERSON>", "externalWorkspace": "Di luar organisasi", "header": "<PERSON><PERSON><PERSON>", "intro_meeting": {"description_1": "<PERSON><PERSON><PERSON> rapat dengan mudah bersama anggota tim kolaboratif", "description_2": "<PERSON><PERSON> rapat baru dengan cepat langsung di tim <PERSON>", "description_3": "Mengkuti informasi rapat tentang waktu dan lokasi atau tinjau catatan dan lampiran dengan mudah", "title_1": "<PERSON><PERSON><PERSON> rapat", "title_2": "Buat rapat baru", "title_3": "Informasi rapat"}, "intro_task": {"description_1": "Mengkategorikan dan mengelompokkan tugas ke dalam setiap daftar", "description_2": "Tam<PERSON>lkan daftar terinci dan status tugas yang harus diselesaikan dalam folder", "description_3": "Membuat dan menyesuaikan item tugas dengan mudah yang sesuai dengan individu dalam tim <PERSON>", "title_1": "<PERSON>ftar tugas", "title_2": "<PERSON>ftar tugas", "title_3": "Tambahkan tugas baru"}, "notiSetting": {"addAssignee": "Tambahkan pelaku", "addWatcher": "Tambahkan pengikut", "checkTaskDone": "Tandai tugas selesai", "deleteTask": "<PERSON><PERSON>", "description": "Sistem akan mengirimkan pesan notifikasi untuk tugas ke tim kolaborasi", "taskOverDue": "Tugas terlambat", "title": "<PERSON><PERSON> notif<PERSON>", "titleSelection": "Terima pemberitahuan ketika", "upcoming": ""}, "notification_config": {"title": "", "tooltip": ""}, "reasonCantEnableCollab": "<PERSON><PERSON> pemimpin tim yang berhak aktifkan fungsi ini"}, "taskCreation": {"addContent": "Tambahkan deskripsi", "addDate": "<PERSON><PERSON><PERSON><PERSON> jadwal", "addDependencies": "Tambahkan tugas yang bergantung", "addPerson": "Melakukan", "addPriority": "Tambahkan prioritas", "addTodo": "Tambahkan", "addTodoButton": "Le<PERSON>h banyak tugas sampingan", "attachFile": "Lam<PERSON>ran", "descriptionHint": "<PERSON><PERSON><PERSON><PERSON> konten tugas", "draft": "<PERSON><PERSON><PERSON>", "failure": {"emptyTaskTitle": "Anda tidak dapat membiarkan judul tugas kosong", "fileDoNotHasExtension": "Tidak dapat mengunggah %1s file yang tidak diformat", "generalTitle": "<PERSON><PERSON><PERSON> kosong", "max16AttachmentFilesMessage": "Unggah hingga 16 lampiran, silakan coba lagi.", "max16AttachmentFilesTitle": "<PERSON><PERSON><PERSON>", "uploadTitle": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat mengunggah"}, "finish": "Menyimpan", "priority": "Prioritas", "todoHint": "<PERSON><PERSON><PERSON>n tugas di sini...", "upload": "Mengunggah"}, "taskFolder": {"confirmDelete": {"accept": "<PERSON><PERSON><PERSON><PERSON>", "accessDenied": "Anda tidak diizinkan untuk menghapus folder ini!", "cancel": "<PERSON><PERSON><PERSON>", "content": "<PERSON><PERSON><PERSON><PERSON> Anda yakin akan menghapus folder ini? Semuda daftar dan tugas dalam folder akan dihapus selamanya.", "contentWhenArchived": "<PERSON><PERSON><PERSON><PERSON> Anda yakin akan menghapus folder ini? Semuda daftar dan tugas dalam proyek akan dihapus se<PERSON>a.", "deleted": "Folder berhasil dihapus!", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "create": "Membuat baru", "createAction": "Membuat", "createFolderSuccessfully": "Folder telah berhasil dibuat", "createHint": "Beri nama folder...", "createTitle": "Buat folder baru", "editAction": "Menyimpan", "emptyFolderContent": "Belum ada daftar tugas dalam folder ini, buat yang baru untuk memulai!", "moreActions": {"add_folder": "Buat folder baru", "add_tasklist": "Buat daftar tugas baru", "archive": "Menyimpan folder", "delete": "Hapus folder", "rename": "Mengganti nama folder", "unarchive": "Pulihkan folder"}, "title": "Folder", "updateFolderSuccessfully": "Folder telah berhasil diupdate"}, "taskGeneral": {"defaultProjectName": "<PERSON><PERSON> saya", "isDone": "<PERSON><PERSON> se<PERSON>", "project": "Proyek", "show": "Tampil", "status": {"allTask": "<PERSON><PERSON><PERSON>", "doneToday": "Harus selesai hari ini", "emptyDoneToday": "Tidak ada tugas lagi hari ini. Mari kita istirahat dan merencanakan tugas yang akan datang!", "emptyNoDueDate": "Tidak ada tugas tanpa batas waktu sekarang.\nMari kita istirahat dan merencanakan tugas yang akan datang!", "emptyUpcoming": "Tidak ada tugas yang akan segera habis batas waktunya .\nMari kita istirahat dan merencanakan tugas yang akan datang!", "noDueDate": "Tugas tanpa batas waktu", "overdue": "Tugas yang terlambat", "upcoming": "Tugas yang akan segera habis batas waktunya"}}, "taskList": {"confirmDelete": {"accept": "<PERSON><PERSON><PERSON><PERSON>", "accessDenied": "Anda tidak memiliki izin untuk menghapus daftar tugas ini!", "cancel": "<PERSON><PERSON><PERSON>", "content": "<PERSON><PERSON><PERSON><PERSON> Anda yakin menghapus daftar tugas ini? Semua tugas dalam daftar tugas ini akan dihapus se<PERSON>a.", "contentWhenArchived": "<PERSON><PERSON><PERSON><PERSON> Anda yakin menghapus daftar tugas ini? Semua tugas dalam proyek akan dihapus se<PERSON>a.", "deleted": "Daftar tugas telah berhasil di<PERSON>!", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "createAction": "Membuat", "createHint": "Beri nama daftar tugas ...", "createTaskListButtonLabel": "Tambahkan daftar baru", "createTaskListSuccessfully": "Daftar tugas telah berhasil dibuat", "createTitle": "Buat daftar tugas baru", "editAction": "Menyimpan", "moreActions": {"add": "Buat tugas baru", "archive": "Menyimpan daftar tugas", "delete": "<PERSON><PERSON> daftar tugas", "rename": "Ganti nama daftar tugas", "unarchive": "Pulihkan daftar tugas"}, "noTaskDescription": "Tidak ada tugas untuk ditampilkan", "noTaskTitle": "Tidak ada tugas", "title": "<PERSON>ftar tugas", "updateTasklistSuccessfully": "<PERSON><PERSON><PERSON><PERSON> daftar tugas"}, "taskProject": {"add": "Tambah", "addNameForProject": "Tambahkan nama untuk proyek", "addNewMemberSuccessfully": "Menambahkan anggota ke proyek", "addNewMembersSuccessfully": "Menambahkan anggota ke proyek", "addNewProject": "Tambahkan proyek baru", "archive": "Penyimpanan", "archive_later": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "collabEditMember": {"description": "", "title": ""}, "confirmDelete": {"accept": "<PERSON><PERSON><PERSON><PERSON>", "accessDenied": "Anda tidak boleh menghapus proyek ini!", "cancel": "<PERSON><PERSON><PERSON>", "content": "<PERSON><PERSON><PERSON><PERSON> Anda yakin menghapus proyek ini? Se<PERSON>a daftar dan tugas dalam proyek akan dihapus se<PERSON>a.", "contentWhenArchived": "<PERSON><PERSON><PERSON><PERSON> Anda yakin menghapus proyek ini? Se<PERSON>a daftar dan pekerjaan dalam proyek akan dihapus se<PERSON>a.", "deleted": "Proyek ber<PERSON><PERSON>!", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "confirmLeave": {"collabDescription": "", "description": "Anda tidak akan dapat mengakses tugas dalam proyek ini. Apakah Anda yakin ingin mening<PERSON>kan?", "discard": "Membatalkan", "leave": "Meninggalkan", "title": "Meninggalkan proyek"}, "confirm_discard_project_editing": "Anda yakin ingin membatalkan pengeditan? Semua pengeditan yang baru saja Anda buat tidak akan disimpan.", "createNewProject": "Buat proyek baru...", "createProjectSuccessfully": "Proyek telah berhasil dibuat", "department": "Departemen", "discard": "Membatalkan", "discard_editing": "Batalkan pengeditan", "editProject": "<PERSON><PERSON><PERSON> proyek", "edited_project_successfully": "<PERSON><PERSON><PERSON><PERSON> mengedit proyek", "emptyTaskListTitle": "Tidak ada daftar tugas", "emptyTitle": "Tidak ada proyek", "filter": {"all": "", "collabProject": "", "collabProjectHeader": "", "generalProject": "", "generalProjectHeader": ""}, "group_chat": "Grup obrolan", "leaveProjectSuccessfully": "Sudah meninggalkan proyek", "member": "Anggota", "moreActions": {"add": "Buat folder atau daftar baru", "addNewMember": "Tambahkan anggota", "archive": "Menyimpan proyek", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON> proyek", "leavingTheProject": "Meninggalkan proyek", "rename": "Mengganti nama proyek", "unarchive": "Memulihkan proyek", "viewReport": "Melihat laporan proyek"}, "myTask": "<PERSON><PERSON> saya", "name": "<PERSON><PERSON>k saya", "note1_invite_member": "Hanya anggota organisasi ini yang dapat ditambahkan.", "note2_invite_member": "<PERSON>ya anggota proyek ini yang dapat melihat dan mengedit tugas proyek.", "note_invite_member": "<PERSON>ya anggota proyek ini yang dapat melihat dan mengedit tugas proyek.", "openProjectList": "<PERSON>uka daftar proyek", "pin": {"pin": "", "pin_limit": "", "pin_success": "", "unpin": "", "unpin_success": ""}, "project": "", "projectEmpty": "Konten tidak ada", "projectNoAccess": "Atau tidak memiliki akses", "project_members": "<PERSON><PERSON><PERSON> proyek", "report": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>", "setProjectColor": "<PERSON><PERSON><PERSON> warna untuk proyek", "setProjectIcon": "Pi<PERSON>h ikon untuk proyek", "setProjectName": "<PERSON>ri nama proyek...", "titleAddMember": "Tambahkan anggota", "unarchive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateProjectSuccessfully": "Proyek telah ber<PERSON>il diupdate"}, "test": "Pemeriks<PERSON>", "text": {"show_less": "", "show_more": "", "text": {"show_less": "<PERSON><PERSON><PERSON> ma<PERSON>kkan <PERSON> tugas", "show_more": "<PERSON><PERSON><PERSON> masukkan judul tugas", "title": ""}}, "ticket": {"activity": {"detail": {"reason": "", "requester": ""}, "type_with_detail": {"additional_title": "", "back_step": "", "onhold_title": "", "reopen_title": "", "spam_title": ""}, "view_detail": ""}, "category": {"request": "", "waiting_for_archive": ""}, "create": {"action_btn": "", "create_success": "", "input": {"base_attachment": {"not_pick_data": {"hint": ""}}, "datetime": {"am_pm": {"am": "", "pm": "", "title": ""}, "date_picker_title": "", "days": "", "hours": "", "minutes": "", "part_of_day_picker_title": "", "time_picker_title": "", "time_picker_title_hint": "", "validator": {"end_before_start": "", "start_after_end": ""}}, "department": {"done_btn": "", "title": ""}, "media": {"add": "", "view_all": ""}, "member": {"done_btn": "", "title": ""}, "number": {"btn": {"done": ""}, "currency": {"hint": "", "title": ""}, "validator": {"empty": "", "format": "", "max": "", "min": "", "range": ""}}, "priority": {"action": "", "popup_title": "", "title": ""}, "privacy": {"hint": "", "title": ""}, "relative_request": {"action": "", "popup_pick": {"done_btn": "", "search_hint": "", "title": ""}, "title": ""}, "request": {"action": "", "cancel": "", "search": {"hint": ""}, "title": ""}, "show_less": "", "show_more": "", "table": {"button": {"add_a_row": ""}, "need_atleast_one_row": "", "row": "", "success": {"add_a_row": "", "duplicate_a_row": "", "remove_a_row": ""}}, "title": {"edit_text": "", "hint": "", "no_answer": "", "no_required": "", "required": "", "title": "", "validator": {"empty": ""}}, "validator": {"empty": "", "snackbar_empty": ""}}, "title": ""}, "datetime": {"am": "", "am_shorten": "", "pm": "", "pm_shorten": ""}, "details": {"actions": {"add_handler": "", "add_handler_title": "", "add_label": "", "add_watcher": "", "approve": "", "approve_archive": "", "archive": "", "back_last_step": "", "back_step": "", "cancel": "", "change_handler": "", "change_handler_title": "", "chat": "", "close": "", "continue_handle": "", "copy": "", "delete": "", "edit": "", "manage_follower": "", "more": "", "next_step": "", "no_approve": "", "open": "", "provide_info": "", "reject_archive": "", "report_spam": "", "unfollow": ""}, "add_follower": {"all_steps": {"description": "", "title": ""}, "current_step": {"description": "", "title": ""}, "remove_all_steps": {"description": "", "title": ""}, "remove_current_step": {"description": "", "title": ""}, "success": "", "success_all_step": "", "title": ""}, "add_handler_success": "", "add_label_success": "", "additional_request_edit": {"appbar": {"action_btn": "", "title": ""}, "body": {"attachments": {"action_btn": "", "title": ""}, "hint": "", "provide_info": {"hint": "", "title": ""}}}, "appbar": {"rtf_title": "", "title": ""}, "approve_on_hold_success": "", "approve_success": "", "back_last_step_popup": {"title": ""}, "back_step_popup": {"back_success": "", "reason": "", "reason_max": "", "reason_placeholder": "", "select_step_empty": "", "step_back_to": "", "submit": "", "title": ""}, "cancel_success": "", "change_handler_success": "", "close_success": "", "comment_tabs": {"comment_hint": "", "empty": ""}, "delete_success": "", "detail_tab": {"code": "", "code_copied": "", "customer_name": "", "empty_data": "", "priority": "", "provided_content": "", "response_content": "", "ticket_content": "", "ticket_relative": "", "type": ""}, "duplicate": {"appbar_title": "", "snackbar": {"success": ""}, "ticket_title": ""}, "edit": {"action": "", "snackbar": {"success": ""}, "title": "", "validation": {"can_not_edit": ""}}, "fill_require_fields_error": "", "move_to_on_hold": {"action": "", "hint": "", "label": "", "success": "", "title": "", "validator": {"empty": ""}}, "next_step_success": "", "on_hold_need_approve": "", "on_hold_success": "", "provide_info": {"response_btn": "", "title": ""}, "reject_on_hold_success": "", "reject_success": "", "reopen": {"action": "", "title": ""}, "reopen_success": "", "report_spam_success": "", "request_to_provide_info": {"action": "", "hint": "", "label": "", "success": "", "title": "", "validator": {"empty": ""}}, "review": {"placeholder": "", "reopen_btn_p1": "", "reopen_btn_p2": "", "reopen_btn_p3": "", "submit_btn": "", "title": ""}, "sla": {"actual": "", "assignee": "", "branch": "", "complete": "", "day": "", "detail": "", "first_response": "", "follower": "", "hour": "", "info": {"follower": "", "handler": "", "label": "", "supporter": "", "title": ""}, "minute": "", "no_item": "", "no_response": "", "node_actual_time": "", "overdue": "", "parallel": "", "popup": {"collab": "", "department": "", "member": "", "role": ""}, "remaining": "", "resolve_time": "", "second": "", "submited": "", "supporter": "", "tag": "", "title": ""}, "spam": {"action": "", "hint": "", "label": "", "success": "", "title": "", "validator": {"empty": ""}}, "tabs": {"activity": "", "comment": "", "detail": "", "sla": ""}, "tag": {"done_btn": ""}, "unfollow_success": ""}, "filter_screen": {"add": "", "add_with_icon": "", "apply": "", "creator": "", "end_date": "", "end_day": "", "filter": "", "filter_pick_create": "", "filter_pick_date_warning": "", "filter_pick_date_warning_2": "", "filter_pick_handler": "", "filter_pick_label": "", "filter_pick_member": "", "filter_pick_member_done": "", "filter_pick_workflow_done": "", "filter_v2": "", "implementer": "", "label": "", "priority": "", "request_status": "", "request_type": "", "reset_filter": "", "select": "", "start_date": "", "start_day": "", "step_status": "", "time_range": ""}, "home_menu": {"archive_created_by_me": "", "archive_send_to_me": "", "created_by_me": "", "created_by_my_department": "", "flow_management": "", "follow": "", "menu_title": "", "no_handler": "", "send_to_me": "", "send_to_my_assignee": "", "send_to_my_group": "", "tabs": {"archive_created_by_me": {"approved": "", "not_approve": "", "waiting_for_approve": ""}, "archive_send_to_me": {"approved": "", "not_approve": "", "waiting_for_approve": ""}, "created_by_me": {"all": "", "archived": "", "cancelled": "", "closed": "", "handled": "", "handling": "", "out_of_date": "", "spam": "", "waiting_for_handling": ""}, "created_by_my_department": {"all": "", "archived": "", "cancelled": "", "closed": "", "handled": "", "handling": "", "out_of_date": "", "spam": "", "waiting_for_handling": ""}, "flow_management": {"all": "", "approved": "", "archived": "", "cancelled": "", "handled": "", "handling": "", "not_approve": "", "out_of_date": "", "spam": "", "waiting_for_archive": "", "waiting_for_handling": "", "waiting_for_provide_info": ""}, "follow": {"all": "", "archived": "", "cancelled": "", "closed": "", "handled": "", "handling": "", "out_of_date": "", "spam": "", "waiting_for_handling": ""}, "no_handler": {"all": "", "out_of_date": ""}, "search_by_code": "", "search_by_name": "", "search_hint": "", "send_to_me": {"all": "", "approved": "", "archived": "", "cancelled": "", "handled": "", "handling": "", "not_approve": "", "out_of_date": "", "spam": "", "waiting_for_archive": "", "waiting_for_handling": "", "waiting_for_provide_info": ""}, "send_to_my_assignee": {"all": "", "approved": "", "archived": "", "cancelled": "", "handled": "", "handling": "", "not_approve": "", "out_of_date": "", "spam": "", "waiting_for_archive": "", "waiting_for_handling": "", "waiting_for_provide_info": ""}, "send_to_my_group": {"all": "", "approved": "", "archived": "", "cancelled": "", "handled": "", "handling": "", "not_approve": "", "out_of_date": "", "spam": "", "waiting_for_archive": "", "waiting_for_handling": "", "waiting_for_provide_info": ""}}}, "main": {"title": ""}, "node": {"item": {"waiting_for_archive": "", "waiting_for_provide_info": ""}, "status": {"approved": "", "archived": "", "cancelled": "", "handled": "", "handling": "", "not_approve": "", "not_turn_yet": "", "rejected": "", "skipped": "", "spam": "", "waiting_for_archive": "", "waiting_for_handling": "", "waiting_for_provide_info": ""}}, "popup": {"confirm": {"approve": {"content": {"first": "", "second": ""}, "left_btn": "", "right_btn": "", "title": ""}, "approve_archive": {"content": {"first": "", "second": ""}, "left_btn": "", "right_btn": "", "title": ""}, "cancel_ticket": {"content": {"first": "", "second": ""}, "left_btn": "", "right_btn": "", "title": ""}, "close": {"content": "", "left_btn": "", "right_btn": "", "title": ""}, "continue_handling": {"content": {"first": "", "second": ""}, "left_btn": "", "right_btn": "", "title": ""}, "delete": {"content": {"first": "", "second": ""}, "left_btn": "", "right_btn": "", "title": ""}, "move_to_on_hold": {"content": {"first": "", "second": ""}, "left_btn": "", "right_btn": "", "title": ""}, "no": "", "reject": {"content": {"first": "", "second": ""}, "left_btn": "", "right_btn": "", "title": ""}, "reject_archive": {"content": {"first": "", "second": ""}, "left_btn": "", "right_btn": "", "title": ""}, "unfollow": {"content": "", "left_btn": "", "right_btn": "", "title": ""}, "update_node_status": {"content": {"first": "", "second": ""}, "left_btn": "", "right_btn": "", "title": ""}}, "info": {"no_label": {"content": "", "left_btn": "", "title": ""}, "no_workflow": {"content": "", "left_btn": "", "title": ""}}, "label": {"done_btn": "", "search_hint": "", "title": ""}}, "priority": {"high": "", "low": "", "medium": "", "urgent": ""}, "search_screen": {"hint": ""}, "status": {"archived": "", "cancelled": "", "closed": "", "deleted": "", "handled": "", "handling": "", "spam": "", "unknow": "", "waiting_for_handling": ""}}, "time": {"days_after_many": "% hari kemudian", "days_after_one": "% hari kemudian", "days_after_zero": "% hari kemudian", "days_ago_many": "% hari yang lalu", "days_ago_one": "% hari yang lalu", "days_ago_zero": "% hari yang lalu", "hours_after_many": "% jam kemudian", "hours_after_one": "% jam kemudian", "hours_after_zero": "% jam kemudian", "hours_ago_many": "% jam yang lalu", "hours_ago_one": "% jam yang lalu", "hours_ago_zero": "% jam yang lalu", "just_in_time": "", "just_now": "", "minute_many": "", "minute_one": "Tepat waktu", "minute_zero": "Baru saja selesai", "minutes_after_many": "% menit", "minutes_after_one": "% menit", "minutes_after_zero": "% menit", "minutes_ago_many": "% menit kemudian", "minutes_ago_one": "% menit kemudian", "minutes_ago_zero": "% menit kemudian", "months_after_many": "% menit yang lalu", "months_after_one": "% menit yang lalu", "months_after_zero": "% menit yang lalu", "months_ago_many": "% bulan depan", "months_ago_one": "% bulan depan", "months_ago_zero": "% bulan depan", "short": {"day": "", "hour": "", "minute": "", "second": ""}, "today": "% bulan lalu", "tomorrow": "% bulan lalu", "weeks_after_many": "% bulan lalu", "weeks_after_one": "<PERSON> ini", "weeks_after_zero": "Besok", "weeks_ago_many": "% minggu depan", "weeks_ago_one": "% minggu depan", "weeks_ago_zero": "% minggu depan", "years_after_many": "% minggu lalu", "years_after_one": "% minggu lalu", "years_after_zero": "% minggu lalu", "years_ago_many": "% tahun depan", "years_ago_one": "% tahun depan", "years_ago_zero": "% tahun depan", "yesterday": "% tahun lalu"}, "timeKeeping": {"ableToCheckIn": "% tahun lalu", "ableToCheckOut": "% tahun lalu", "afternoon": "<PERSON><PERSON><PERSON>", "approved": "Dapat check -in dari", "at": "", "attachment_text": "", "attachments_text": "", "cancel": "Dapat memeriksa", "canceled": "Sore", "cantGetLocationDescription": "Disetuju<PERSON>", "cantGetLocationTitle": "<PERSON><PERSON><PERSON>", "checkIn": "Di<PERSON><PERSON><PERSON>", "checkInAt": "", "checkInBefore": "", "checkInCheckOutTime": "Mendaftar", "checkInSuccessed": "Mendaftar", "checkInWithoutShift": "Check -in sebelumnya", "checkOut": "Check -in/check out", "checkOutAt": "Anda sudah check -in", "contentCameraSetting": "Mendaftar", "contentLocationSetting": "Periksa", "done": "Periksa", "earlyOut": "Izinkan GapoWork mengakses kamera Anda untuk mengambil foto", "endTime": "Izinkan GapoWork mengakses lokasi Anda untuk ketepatan waktu", "errorWifiConnection": "", "explanation_button_title": "", "explanation_form_disabled_text": "", "explanation_state_description": {"approved": "", "canceled": "", "created": "", "declined": "", "unknown": ""}, "flexHours": "<PERSON><PERSON><PERSON> awal", "flexTimeHour": "<PERSON><PERSON><PERSON>", "fromDate": "Tidak terhubung ke wifi", "haveRequest": "jam", "holiday": "", "holiday_list": "", "hour": "Shift fleksibel", "lastCheckOut": "<PERSON><PERSON>", "lateIn": "<PERSON><PERSON><PERSON><PERSON> per<PERSON>", "lateInEarlyOut": "jam", "lateInLeaveEarly": "Check out terbaru", "minute": "Terlambat", "morning": "Terlambat/awal/lupa check in/out", "noRequest": "Terlambat/lebih awal", "noShiftName": "menit", "noShiftToday": "<PERSON><PERSON>", "notCheckIn": "Tidak ada permintaan", "notCheckOut": "", "notFoundShift": "Anda tidak memiliki shift hari ini", "notRecorded": "Tidak check -in", "notSetLocation": "Tidak checkout", "number": "<PERSON><PERSON> tidak men<PERSON>n <PERSON> <PERSON>a", "on": "", "onTime": "<PERSON><PERSON><PERSON> se<PERSON>ai k<PERSON>n", "openSetting": "Organisasi Anda belum menetapkan lokasi untuk pencegahan", "outOfCheckInTime": "TIDAK", "pending": "Tepat waktu", "permission": {"disable_device": "Pengatura<PERSON> terbuka", "disable_device_descrition": "Tidak dapat check -in karena keluar dari waktu check -in", "unregister_device": "Tertunda", "unregister_device_description": "", "use_other_device": "", "use_other_device_description": ""}, "popup": {"400101": "", "400102": "", "can_not_get_location": {"description": {"ios_close": "", "ios_cta": "", "primary_span": "", "secondary_span": "", "tertiary_span": ""}, "sub_descripton": "", "title": ""}, "exceed_time": "", "no_precise_location": {"description": "", "title": ""}, "not_enough_images": ""}, "rejected": "<PERSON><PERSON><PERSON>", "requestReason": "<PERSON><PERSON><PERSON>", "requestTab": "Me<PERSON><PERSON>", "shift": "<PERSON><PERSON><PERSON><PERSON>", "shiftCode": "Kode shift", "shiftDetail": "Pergeseran detail", "shiftFirstHalf": "<PERSON><PERSON><PERSON><PERSON>", "shiftLaterHalf": "<PERSON><PERSON><PERSON><PERSON>", "shiftName": "<PERSON><PERSON><PERSON><PERSON>", "shiftTab": "<PERSON><PERSON><PERSON><PERSON>/pergi lebih awal", "startTime": "Meninggalkan", "statistics": {"lateInLeaveEarly": "<PERSON> kerja", "leave": "", "workingDay": ""}, "successCheckInTitle": "", "successCheckOutTitle": "", "tab": {"approval": "", "timeKeeping": "", "timeSheet": "", "workSheet": ""}, "timeKeepingLogTab": "Log ketepatan waktu", "title": "<PERSON><PERSON>", "titleCameraSetting": "Gapowork ingin mengakses kamera Anda", "titleLocationSetting": "Gapowork ingin mengakses lokasi Anda", "toDate": "Hingga saat ini", "today": "Ketepatan waktu", "today_is": "", "totalWorkingTime": "<PERSON><PERSON><PERSON> kerja", "totalWorkingTimeText": "Total waktu kerja", "tryAgain": "Coba lagi", "unavailableCheckIn": "Total waktu kerja", "understood": "OKE", "workingHour": "", "youHave": ""}, "validation": {"permTaskUpdateContent": "", "permTaskUpdateTitle": "", "permTaskViewTitle": "", "taskDescription": "", "taskTitle": ""}, "view_all": "", "view_less": ""}