/*
 * Created Date: Tuesday, 23rd July 2024, 08:30:11
 * Author: gapo
 * -----
 * Last Modified: Friday, 11th April 2025 17:51:08
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

import 'package:gp_core/core.dart';
import 'package:gp_shared/domain/entity/base/base_list.entity.dart';

import '../../../../domain/entity/entity.dart';

const _formFieldTypeCanEdit = <TicketFormFieldType>[
  TicketFormFieldType.inputText,
  TicketFormFieldType.inputNumber,
  TicketFormFieldType.inputCurrency,
  TicketFormFieldType.inputSelection,
  TicketFormFieldType.inputSelections,
  TicketFormFieldType.inputDatetime,
  TicketFormFieldType.inputDateRange,
  TicketFormFieldType.inputPickMedias,
  TicketFormFieldType.inputPickAttachments,
  TicketFormFieldType.inputPickDepartment,
  TicketFormFieldType.inputPickMember,
  TicketFormFieldType.inputAddress,
  TicketFormFieldType.inputPhoneNumber,
  TicketFormFieldType.inputTable,
];

final class TicketEntity extends BaseListEntity {
  TicketEntity({
    super.id,
    required this.code,
    required this.title,
    required this.assigneeId,
    required this.currentNodeId,
    required this.priority,
    required this.status,
    required this.nodeStatus,
    required this.workspaceId,
    required this.workflowId,
    this.workflow,
    this.currentNodeName,
    this.reopened,
    this.ratingPoint,
    this.isPrivate,
    this.closedAt,
    this.nodeDeadlineAt,
    this.latestRecentActivityAt,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.deadlineFirstResponseAt,
    this.firstResponseAt,
    this.deadlineProcessAt,
    this.processAt,
    this.absoluteSecondsUntilResponseDeadline,
    this.absoluteSecondsUntilProcessDeadline,
    this.createdBy,
    this.updatedBy,
    this.creator,
    this.assignee,
    this.sla,
    this.recentActivity,
    this.userRole,
    this.fieldValues,
    this.refTickets,
    this.refTicketIds,
    this.adminsWorkflowGroup,
    this.review,
    this.adminsWorkflow,
  });

  final String title;
  final String? assigneeId, currentNodeId;

  /// prefix_code + generated number in base 36
  final String code;

  final TicketPriority priority;
  final TicketStatus status;

  final TicketNodeStatus? nodeStatus;

  final String workspaceId;

  // BE lúc int, lúc string...
  final dynamic workflowId;

  final String? currentNodeName;

  final bool? isPrivate;

  // datetime
  final DateTime? closedAt,
      nodeDeadlineAt,
      latestRecentActivityAt,
      createdAt,
      updatedAt,
      deletedAt,
      deadlineFirstResponseAt,
      firstResponseAt,
      deadlineProcessAt,
      processAt;

  final Duration? absoluteSecondsUntilResponseDeadline;

  final Duration? absoluteSecondsUntilProcessDeadline;

  final int? createdBy, updatedBy;

  final int? reopened, ratingPoint;

  final WorkFlowEntity? workflow;

  final TicketCreatorEntity? creator, assignee;

  final List<TicketAssigneeEntity>? adminsWorkflowGroup;
  final List<TicketAssigneeEntity>? adminsWorkflow;

  final TicketSLAEntity? sla;

  final TicketActivityEntity? recentActivity;

  final TickeUserRoleEntity? userRole;

  @JsonKey(name: 'field_values')
  List<WorkFlowFieldValuesEntity>? fieldValues;

  final List<RefTicketEntity>? refTickets;
  final List<dynamic>? refTicketIds;

  final String? review;

  // TicketOnHoldRequestInModeResponse

  late String createdAtStr = createdAt != null
      ? DateFormat('HH:mm dd/MM/yyyy').format(createdAt!)
      : '';

  String get updatedAtStr => updatedAt != null
      ? DateFormat('HH:mm dd/MM/yyyy').format(updatedAt!)
      : '';

  String get displayCode {
    return '#$code';
  }

  bool get canEdit {
    if (workflow?.form?.fields == null ||
        workflow?.form?.fields.isEmpty == true) {
      return false;
    }

    for (var e in workflow!.form!.fields) {
      if (_formFieldTypeCanEdit.contains(e.type)) {
        return true;
      }
    }

    return false;
  }

  List<Map<String, dynamic>> toValuesJson() {
    if (fieldValues == null || fieldValues?.isEmpty == true) {
      return [];
    }

    final ret = <Map<String, dynamic>>[];

    for (var element in fieldValues ?? <WorkFlowFieldValuesEntity>[]) {
      ret.add(element.toValueJson());
    }

    return ret;
  }

  bool get showRating {
    return status == TicketStatus.handled &&
        workflow?.advanceSetting?.rateOption?.isAllowed == true &&
        userRole?.requester == true;
  }

  bool get hasReview {
    return ratingPoint != null;
  }

  bool get isRequester => userRole?.requester ?? false;

  bool get canAddFollowerAllStep => userRole?.userWhoIsAdminWorkflow ?? false;

  bool get canUnfollowAllStep => userRole?.userWhoIsAdminWorkflow ?? false;

  TicketEntity copyWith({
    List<WorkFlowFieldValuesEntity>? fieldValues,
  }) {
    return TicketEntity(
      id: id,
      code: code,
      title: title,
      assigneeId: assigneeId,
      currentNodeId: currentNodeId,
      priority: priority,
      status: status,
      nodeStatus: nodeStatus,
      workspaceId: workspaceId,
      workflowId: workflowId,
      fieldValues: fieldValues,
    );
  }
}

final class RefTicketEntity {
  RefTicketEntity({
    required this.id,
    required this.code,
    required this.title,
    required this.status,
    this.createdAt,
    this.createBy,
    this.createdByInfo,
  });

  final dynamic id;
  final String code;
  final String title;
  final DateTime? createdAt;
  final dynamic createBy;
  final TicketStatus status;

  final TicketCreatorEntity? createdByInfo;

  late String createdAtStr = createdAt != null
      ? DateFormat('HH:mm dd/MM/yyyy').format(createdAt!)
      : '';
}

extension WorkFlowFieldValuesEntityCollectionExt
    on List<WorkFlowFieldValuesEntity> {
  void addValueFromWorkFormField(TicketEntity ticketEntity) {
    final form = ticketEntity.workflow?.form;
    final fields = form?.fields;

    if (fields != null) {
      for (var element in fields) {
        if (element.type == TicketFormFieldType.textLabel) {
          // ToanNM: chả biết đầu nguồn ở đâu, fix tạm ở lưng chừng.
          element.currentText = element.option.content;

          add(WorkFlowFieldValuesEntity(
            workspaceId: form?.workspaceId ?? '',
            fieldId: element.id,
            ticketId: ticketEntity.id,
            unitId: element.option.unit,
            info: element,
            value: element.option.content,
          ));
        }
      }
    }
  }
}
