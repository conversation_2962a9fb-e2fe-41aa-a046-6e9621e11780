import 'package:diffutil_dart/diffutil.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_ticket/data/model/request/ticket/details/follower/ticket_follower_params.dart';
import 'package:gp_feat_ticket/presentation/create/form/widgets/picker/member_picker_params.dart';
import 'package:gp_shared/domain/entity/assignee/assignee.entity.dart';
import 'package:gp_shared/domain/entity/select_invite_options.entity.dart';
import 'package:gp_shared/widgets/member_picker/member_picker_result.dart';
import 'package:gp_shared/widgets/member_picker/member_picker_wrapper.dart';

class FollowerChanges {
  final List<AssigneeEntity> addedMembers;
  final List<AssigneeEntity> removedMembers;

  const FollowerChanges({
    this.addedMembers = const [],
    this.removedMembers = const [],
  });

  bool get isEmpty => addedMembers.isEmpty && removedMembers.isEmpty;

  TicketFollowerParams toTicketFollowerParams() {
    return TicketFollowerParams(
      addAssignees: addedMembers
          .map((member) => TicketParticipantsParams(
                id: member.id.toString(),
                type: 1, // Type 1 for followers
              ))
          .toList(),
      removeAssignees: removedMembers
          .map((member) => TicketParticipantsParams(
                id: member.id.toString(),
                type: 1, // Type 1 for followers
              ))
          .toList(),
    );
  }
}

class FollowerUtils {
  /// Pick members using the member picker
  static Future<List<AssigneeEntity>> pickMembers({
    List<AssigneeEntity>? assigneeEntities,
    List<int>? notRemovableUserIds,
    required SelectInviteesOptionsMode pickerMode,
    bool closeOverlayWhenBack = true,
  }) async {
    final MemberPickerResult<SelectMemberEntity>? result =
        await GetIt.I<MemberPickerWrapper>().pickMember(
      TicketMemberPickerParams.pickMember(
        pickerMode: pickerMode,
        currentSelectMemberEntity:
            SelectMemberEntity(assigneeEntities: assigneeEntities),
        closeOverlayWhenBack: closeOverlayWhenBack,
        notRemovableUserIds: notRemovableUserIds,
      ),
    );

    final entities = result?.resultEntity?.assigneeEntities ?? [];
    logDebug('pickMembers entities -> $entities');

    return entities;
  }

  /// Calculate changes between original and new follower lists
  static FollowerChanges calculateFollowerChanges(
    List<AssigneeEntity> originalFollowers,
    List<AssigneeEntity> newFollowers,
  ) {
    final addedMembers = <AssigneeEntity>[];
    final removedMembers = <AssigneeEntity>[];

    if (originalFollowers.isEmpty && newFollowers.isEmpty) {
      return FollowerChanges();
    }

    final diffResult = calculateListDiff<AssigneeEntity>(
      originalFollowers,
      newFollowers,
      detectMoves: true,
      equalityChecker: (o1, o2) => o1.id == o2.id,
    );

    final updateResults = diffResult.getUpdatesWithData();

    if (updateResults.isEmpty) {
      return FollowerChanges();
    }

    for (final updateResult in updateResults) {
      updateResult.when(
        insert: (pos, data) {
          addedMembers.add(data);
        },
        remove: (pos, data) {
          removedMembers.add(data);
        },
        change: (pos, p1, p2) {
          // doNothing
        },
        move: (from, to, data) {
          // doNothing
        },
      );
    }

    return FollowerChanges(
      addedMembers: addedMembers,
      removedMembers: removedMembers,
    );
  }
}
