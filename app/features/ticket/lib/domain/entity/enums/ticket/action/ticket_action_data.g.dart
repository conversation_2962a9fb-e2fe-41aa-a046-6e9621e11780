// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ticket_action_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TicketActionEntity _$TicketActionEntityFromJson(Map<String, dynamic> json) =>
    TicketActionEntity(
      actions: (json['buttons'] as List<dynamic>)
          .map((e) => $enumDecode(_$TicketActionEnumMap, e))
          .toList(),
      highlightFirstButton: json['highlightFirstButton'] as bool? ?? false,
      highlightSecondaryButton:
          json['highlightSecondaryButton'] as bool? ?? false,
    );

Map<String, dynamic> _$TicketActionEntityToJson(TicketActionEntity instance) =>
    <String, dynamic>{
      'buttons':
          instance.actions.map((e) => _$TicketActionEnumMap[e]!).toList(),
      'highlightFirstButton': instance.highlightFirstButton,
      'highlightSecondaryButton': instance.highlightSecondaryButton,
    };

const _$TicketActionEnumMap = {
  TicketAction.more: 'more',
  TicketAction.comment: 'comment',
  TicketAction.provideInfo: 'provideInfo',
  TicketAction.edit: 'edit',
  TicketAction.duplicate: 'duplicate',
  TicketAction.editField: 'editField',
  TicketAction.closeTicket: 'closeTicket',
  TicketAction.reopenTicket: 'reopenTicket',
  TicketAction.spam: 'spam',
  TicketAction.chat: 'chat',
  TicketAction.cancelTicket: 'cancelTicket',
  TicketAction.deleteTicket: 'deleteTicket',
  TicketAction.approve: 'approve',
  TicketAction.reject: 'reject',
  TicketAction.nextStep: 'nextStep',
  TicketAction.addHandler: 'addHandler',
  TicketAction.changeHandler: 'changeHandler',
  TicketAction.requestToProvideInfo: 'requestToProvideInfo',
  TicketAction.moveToOnHold: 'moveToOnHold',
  TicketAction.continueHandle: 'continueHandle',
  TicketAction.approveOnHold: 'approveOnHold',
  TicketAction.rejectOnHold: 'rejectOnHold',
  TicketAction.manageFollower: 'manageFollower',
  TicketAction.addLabel: 'addLabel',
  TicketAction.backStep: 'backStep',
  TicketAction.backEndStep: 'backEndStep',
};
