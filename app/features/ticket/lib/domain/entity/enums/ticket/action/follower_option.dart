import 'package:gp_core/core.dart';

/// Enum representing the add follower options
enum ManageFollowerOption {
  /// Add to current step only
  addCurrentStep,

  /// Add to all steps
  addAllSteps,

  /// Remove from current step only
  removeCurrentStep,

  /// Remove from all steps
  removeAllSteps,
}

extension AddFollowerOptionExt on ManageFollowerOption {
  /// Display name for the option
  String get displayName {
    switch (this) {
      case ManageFollowerOption.addCurrentStep:
        return LocaleKeys.ticket_details_add_follower_current_step_title.tr;
      case ManageFollowerOption.addAllSteps:
        return LocaleKeys.ticket_details_add_follower_all_steps_title.tr;
        case ManageFollowerOption.removeCurrentStep:
        return LocaleKeys.ticket_details_add_follower_remove_current_step_title.tr;
      case ManageFollowerOption.removeAllSteps:
        return LocaleKeys.ticket_details_add_follower_remove_all_steps_title.tr;
    }
  }

  /// Description for the option
  String get description {
    switch (this) {
      case ManageFollowerOption.addCurrentStep:
        return LocaleKeys
            .ticket_details_add_follower_current_step_description.tr;
      case ManageFollowerOption.addAllSteps:
        return LocaleKeys.ticket_details_add_follower_all_steps_description.tr;
        case ManageFollowerOption.removeCurrentStep:
        return LocaleKeys
            .ticket_details_add_follower_remove_current_step_description.tr;
      case ManageFollowerOption.removeAllSteps:
        return LocaleKeys
            .ticket_details_add_follower_remove_all_steps_description.tr;
    }
  }

  /// Icon asset for the option
  String get iconAsset {
    switch (this) {
      case ManageFollowerOption.addCurrentStep:
        return Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_LINE15_EYE_SVG;
      case ManageFollowerOption.addAllSteps:
        return Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_LINE15_ADD_SVG;
        case ManageFollowerOption.removeCurrentStep:
        return Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_LINE15_EYE_SLASH_SVG;
      case ManageFollowerOption.removeAllSteps:
        return Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_LINE15_BELL_SLASH_SVG;
    }
  }
}

enum UnfollowOption {
  /// Người theo dõi bỏ follow
  normal,

  /// Quản trị quy trình chọn user để bỏ follow
  adminWorkflow,
}
