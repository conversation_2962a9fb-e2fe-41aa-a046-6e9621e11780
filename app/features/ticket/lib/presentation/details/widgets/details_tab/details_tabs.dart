// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_ticket/data/model/request/ticket/details/cancel/ticket_cancel.params.dart';
import 'package:gp_feat_ticket/data/model/request/ticket/details/reopen/reopen.dart';
import 'package:gp_feat_ticket/data/model/request/ticket/details/review/ticket_review.params.dart';
import 'package:gp_feat_ticket/data/model/request/ticket/details/tag/ticket_tag.params.dart';
import 'package:gp_feat_ticket/domain/entity/enums/ticket/ticket.dart';
import 'package:gp_feat_ticket/domain/entity/workflow/workflow_tag.entity.dart';
import 'package:gp_feat_ticket/presentation/details/mixins/mixins.dart';
import 'package:gp_feat_ticket/presentation/details/mixins/node_field_permission.dart';
import 'package:gp_feat_ticket/presentation/details/widgets/popup/back_step/details_back_step_widget.dart';
import 'package:gp_feat_ticket/presentation/details/widgets/popup/details_reopen_widget.dart';
import 'package:gp_feat_ticket/domain/entity/enums/ticket/action/follower_option.dart';
import 'package:gp_feat_ticket/route/route.dart';
import 'package:gp_shared/domain/entity/assignee/assignee.entity.dart';

import '../../../../data/model/request/ticket/details/additional_requests/ticket_additional_requests_params.dart';
import '../../../../data/model/request/ticket/details/follower/ticket_follower_params.dart';
import '../../../../data/model/request/ticket/details/move_to_onhold/ticket_move_to_onhold_params.dart';
import '../../../../data/model/request/ticket/details/spam/ticket_spam.params.dart';
import '../../../../domain/entity/ticket/ticket_additional_request.entity.dart';
import '../../../../domain/entity/ticket/ticket_flowchart.entity.dart';
import '../../../../domain/entity/ticket/ticket_list.entity.dart';
import '../../../../domain/entity/ticket/ticket_node.entity.dart';
import '../../../../domain/entity/ticket/ticket_onhold_request_respose.entity.dart';
import '../../../../widgets/ticket_avatar_widget.dart';
import '../../../create/form/input_dynamic_form_details.dart';
import '../../../create/widgets/input/input_behavior.dart';
import '../../../home/<USER>';
import '../../additional_request/additional_request.page.dart';
import '../../bloc/ticket_details_bloc.dart';
import '../../bloc/ticket_details_event.dart';
import '../../mixins/node_graph.dart';
import '../../ticket_details.page.dart';
import '../shared/rating_star.dart';
import '../shared/ticket_details_container.dart';
import 'bloc/bloc.dart';

final ticketDetailsScrollController = AutoScrollController(axis: Axis.vertical);

typedef TicketDetailsOnUpdateData = void Function(
  TicketEntity? ticketEntity,
  List<TicketAdditionalRequestEntity>? ticketAdditionalRequestEntities,
);

final class TicketDetailsData {
  TicketDetailsData({
    required this.ticketId,
    this.ticketEntity,
    this.flowChartEntity,
    this.nodeEntity,
    this.ticketActionEntity,
    this.ticketOnHoldRequestEntity,
    this.ticketAdditionalRequestEntities,
    this.ticketReloadDataActionEnum,
    this.ticketAssignees,
  });

  final String ticketId;

  TicketEntity? ticketEntity;
  TicketFlowChartEntity? flowChartEntity;
  TicketNodeEntity? nodeEntity;

  TicketActionEntity? ticketActionEntity;
  TicketOnHoldRequestEntity? ticketOnHoldRequestEntity;
  List<TicketAdditionalRequestEntity>? ticketAdditionalRequestEntities;

  TicketReloadDataActionEnum? ticketReloadDataActionEnum;
  List<Assignee>? ticketAssignees;

  TicketDetailsData copyWith({
    TicketEntity? ticketEntity,
    TicketFlowChartEntity? flowChartEntity,
    TicketNodeEntity? nodeEntity,
    TicketActionEntity? ticketActionEntity,
    TicketOnHoldRequestEntity? ticketOnHoldRequestEntity,
    List<TicketAdditionalRequestEntity>? ticketAdditionalRequestEntities,
    TicketReloadDataActionEnum? ticketReloadDataActionEnum,
    List<Assignee>? ticketAssignees,
  }) {
    return TicketDetailsData(
      ticketId: ticketId,
      ticketEntity: ticketEntity ?? this.ticketEntity,
      flowChartEntity: flowChartEntity ?? this.flowChartEntity,
      nodeEntity: nodeEntity ?? this.nodeEntity,
      ticketActionEntity: ticketActionEntity ?? this.ticketActionEntity,
      ticketOnHoldRequestEntity:
          ticketOnHoldRequestEntity ?? this.ticketOnHoldRequestEntity,
      ticketAdditionalRequestEntities: ticketAdditionalRequestEntities ??
          this.ticketAdditionalRequestEntities,
      ticketReloadDataActionEnum:
          ticketReloadDataActionEnum ?? this.ticketReloadDataActionEnum,
      ticketAssignees: ticketAssignees ?? this.ticketAssignees,
    );
  }
}

class TicketDetailsTabWrapperWidget extends StatefulWidget {
  TicketDetailsTabWrapperWidget({
    required TicketDetailsOnUpdateData onUpdateData,
    required this.detailsData,
    this.ticketDetailsBloc,
    super.key,
  });

  TicketDetailsData detailsData;

  final TicketDetailsBloc? ticketDetailsBloc;

  void showLoading() {
    ticketDetailsBloc?.add(const TicketShowLoadingEvent());
  }

  void hideLoading() {
    ticketDetailsBloc?.add(const TicketHideLoadingEvent());
  }

  @override
  State<TicketDetailsTabWrapperWidget> createState() =>
      _TicketDetailsTabWrapperWidgetState();
}

class _TicketDetailsTabWrapperWidgetState
    extends State<TicketDetailsTabWrapperWidget>
    with
        NodeGraphMixin,
        TicketReloadDataMixin,
        NodeFieldPermission,
        AutomaticKeepAliveClientMixin,
        ValidateFieldsMixin {
  late final TicketDetailsTabBloc bloc =
      TicketDetailsTabBloc(onErrorCallback: () {
    widget.hideLoading();
  });

  List<WorkflowFormFieldPermissionEntity> nodeFieldPermissions = [];

  bool isTicketAssignee = false;

  @override
  bool get wantKeepAlive => true;

  bool get needGetOnHold {
    if (widget.detailsData.ticketEntity != null) {
      if (widget.detailsData.nodeEntity?.status ==
          TicketNodeStatus.waitingForArchive) {
        return true;
      }
    }
    return false;
  }

  void handleTicketFlowChartLoaded(
    TicketEntity ticketEntity,
    TicketFlowChartEntity flowChartEntity,
  ) {
    widget.detailsData.flowChartEntity = flowChartEntity;

    String? currentNodeId;

    if (widget.detailsData.nodeEntity == null) {
      final flowGraph = mapListStep(
        edges: flowChartEntity.edges,
        nodes: flowChartEntity.nodes,
      );
      final lastStep = getLastStep(steps: flowGraph);
      final currentNode = getCurrentNodeFocused(
        step: lastStep ?? flowGraph.last,
        userRole: ticketEntity.userRole,
      );
      currentNodeId = currentNode?.id.toString();
    } else {
      currentNodeId = widget.detailsData.nodeEntity?.id.toString();
    }

    bloc.add(
      TicketNodeEvent(
        ticketEntity: ticketEntity,
        flowChartEntity: flowChartEntity,
        nodeId: currentNodeId,
      ),
    );
  }

  void handleTicketDetailsNodeLoaded({
    required TicketEntity ticketEntity,
    required TicketFlowChartEntity flowChartEntity,
    required TicketNodeEntity nodeEntity,
  }) {
    widget.detailsData.nodeEntity = nodeEntity;

    bloc.add(TicketGetIsTicketAssigneeEvent(
        ticketEntity: ticketEntity,
        ticketNodeEntity: nodeEntity,
        ticketFlowChartEntity: flowChartEntity));

    nodeFieldPermissions = getNodeFieldPermissions(
      startNodePermissions: widget.detailsData.flowChartEntity?.nodes.first
          .option?.workflowFormFieldPermissions,
      currentNodePermissions:
          widget.detailsData.nodeEntity?.option.workflowFormFieldPermissions,
      isNodeAssignee: widget.detailsData.nodeEntity?.assignee?.id.toString() ==
          Constants.userId(),
      isTicketRequester:
          widget.detailsData.ticketEntity?.creator?.id == Constants.userId(),
    );

    // hideLoading sau khi load đủ dữ liệu details
    // còn làm gì sau đó nữa thì sau tính tiếp.
    // widget.hideLoading();
  }

  void handleTicketAdditionalRequestSuccess(
    BuildContext context,
    TicketAdditionalRequestSuccess state,
  ) {
    widget.detailsData.ticketReloadDataActionEnum =
        TicketReloadDataActionEnum.requestToProvideInfo;
    final result = handleReloadDataWhenBack(
        ticketReloadDataActionEnum:
            TicketReloadDataActionEnum.requestToProvideInfo);

    // widget.hideLoading();
    Navigator.of(context).pop(result);
    Popup.instance.showSnackBar(
      message: LocaleKeys.ticket_details_request_to_provide_info_success.tr,
      type: SnackbarType.success,
    );
  }

  void handleTicketGetAssigneesSuccess(
      BuildContext context, TicketGetAssigneesSuccess state) {
    widget.detailsData.ticketAssignees = state.assignees;
    widget.hideLoading();
  }

  void handleTicketGetAdminWorkflowSuccess(
      BuildContext context, TicketGetAdminWorkflowSuccess state) {
    bloc.add(
      TicketGetActionPermissionsEvent(
        ticketEntity: widget.detailsData.ticketEntity!,
        ticketFlowChartEntity: widget.detailsData.flowChartEntity!,
        ticketNodeEntity: widget.detailsData.nodeEntity!,
        isTicketAssignee: isTicketAssignee,
        adminWorkflow: state.adminWorkflow,
      ),
    );
  }

  void handleTicketGetIsTicketAssigneeSuccess(
      BuildContext context, TicketGetIsTicketAssigneeSuccess state) {
    isTicketAssignee = state.isTicketAssignee;
    final adminWorkflow = widget.detailsData.ticketEntity?.adminsWorkflow;
    bloc.add(TicketGetAssigneesEvent(
      assignees: adminWorkflow,
      type: TicketGetAssigneeType.adminWorkflow,
    ));
  }

  void handleTicketGetActionPermissionsSuccess(
    BuildContext context,
    TicketGetActionPermissionsSuccess state,
  ) {
    widget.detailsData.ticketActionEntity = state.ticketActionEntity;

    /*
      Nếu ở trạng thái onhold, lấy thông tin onhold
    */
    if (needGetOnHold) {
      bloc.add(
        TicketGetOnHoldRequestEvent(
          ticketEntity: widget.detailsData.ticketEntity!,
          ticketNodeEntity: widget.detailsData.nodeEntity!,
        ),
      );
    }

    /*
     13/11: ToanNM nếu chỉ lấy ở trạng thái chờ bổ sung thông tin, sẽ bị thiếu
    */
    // if (ticketEntity != null) {
    //   if (nodeEntity?.status == TicketNodeStatus.waitingForProvideInfo) {
    bloc.add(
      TicketGetAdditionalRequestsEvent(
        ticketEntity: widget.detailsData.ticketEntity!,
        ticketNodeEntity: widget.detailsData.nodeEntity!,
      ),
    );
    // }
    // }
  }

  void handleTicketGetAdditionalRequestsSuccess(
    BuildContext context,
    TicketGetAdditionalRequestsSuccess state,
  ) {
    widget.detailsData.ticketAdditionalRequestEntities =
        state.ticketAdditionalRequestEntity;

    /*
      Đã lấy đủ thông tin details
      => update lại màn hình detailsPage bên ngoài

      Nếu cần lấy thông tin onHold, lấy xong thông tin
      => update lại màn hình detailsPage bên ngoài
    */
    if (!needGetOnHold) {
      reloadTicketDetailsPage();
    }

    final actions = widget.detailsData.ticketActionEntity?.actions ?? [];

    final isChangeAssignee = actions.contains(TicketAction.changeHandler);
    final assignees = widget.detailsData.nodeEntity?.assignees ?? [];
    if (isChangeAssignee) {
      assignees.addAll(widget.detailsData.nodeEntity?.assigneeGroup ?? []);
    }
    bloc.add(
      TicketGetAssigneesEvent(
          assignees: assignees, type: TicketGetAssigneeType.assignee),
    );
  }

  void handleTicketChangeNodeStatusSuccess(
    BuildContext context,
    TicketChangeNodeStatusSuccess state,
  ) {
    final nodeStatus = state.nodeStatus;
    if (nodeStatus == TicketNodeStatus.approved) {
      final result = handleReloadDataWhenBack(
          ticketReloadDataActionEnum: TicketReloadDataActionEnum.approve);
      Navigator.of(context).pop(result);
      Popup.instance.showSnackBar(
        message: LocaleKeys.ticket_details_approve_success.tr,
        type: SnackbarType.success,
      );
    } else if (nodeStatus == TicketNodeStatus.notApprove) {
      final result = handleReloadDataWhenBack(
          ticketReloadDataActionEnum: TicketReloadDataActionEnum.reject);
      Navigator.of(context).pop(result);
      Popup.instance.showSnackBar(
        message: LocaleKeys.ticket_details_reject_success.tr,
        type: SnackbarType.success,
      );
    } else if (nodeStatus == TicketNodeStatus.handling) {
      // Case tiếp tục xử lý
      loadDetails();
      widget.detailsData.ticketReloadDataActionEnum =
          TicketReloadDataActionEnum.continueHandle;
    } else {
      final result = handleReloadDataWhenBack(
          ticketReloadDataActionEnum: TicketReloadDataActionEnum.nextStep);
      Navigator.of(context).pop(result);
      Popup.instance.showSnackBar(
        message: LocaleKeys.ticket_details_next_step_success.tr,
        type: SnackbarType.success,
      );
    }
  }

  void handleTicketSpamSuccess(
    BuildContext context,
    TicketSpamSuccess state,
  ) {
    final result = handleReloadDataWhenBack(
        ticketReloadDataActionEnum: TicketReloadDataActionEnum.spam);
    Navigator.of(context).pop(result);
    Popup.instance.showSnackBar(
      message: LocaleKeys.ticket_details_report_spam_success.tr,
      type: SnackbarType.success,
    );
  }

  void handleTicketMoveToOnHoldSuccess(
    BuildContext context,
    TicketMoveToOnHoldSuccess state,
  ) {
    final isNeedApprove = widget.detailsData.ticketEntity?.workflow
            ?.advanceSetting?.approveBeforeOnHold?.isAllowed ??
        false;
    final result = handleReloadDataWhenBack(
        ticketReloadDataActionEnum: isNeedApprove == false
            ? TicketReloadDataActionEnum.onHold
            : TicketReloadDataActionEnum.onHoldNeedApprove);
    Navigator.of(context).pop(result);
    if (!isNeedApprove) {
      Popup.instance.showSnackBar(
        message: LocaleKeys.ticket_details_on_hold_success.tr,
        type: SnackbarType.success,
      );
    } else {
      Popup.instance.showSnackBar(
        message: LocaleKeys.ticket_details_on_hold_need_approve.tr,
        type: SnackbarType.normal,
      );
    }
  }

  void handleTicketGetOnHoldSuccess(
    BuildContext context,
    TicketGetOnHoldSuccess state,
  ) {
    widget.detailsData.ticketOnHoldRequestEntity =
        state.ticketOnHoldRequestEntity;

    reloadTicketDetailsPage();
  }

  void handleTicketAcceptOnHoldSuccess(
    BuildContext context,
    TicketAcceptOnHoldSuccess state,
  ) {
    final result = handleReloadDataWhenBack(
        ticketReloadDataActionEnum: TicketReloadDataActionEnum.approveOnHold);
    Navigator.of(context).pop(result);
    Popup.instance.showSnackBar(
      message: LocaleKeys.ticket_details_approve_on_hold_success.tr,
      type: SnackbarType.success,
    );
  }

  void handleTicketRejectOnHoldSuccess(
    BuildContext context,
    TicketRejectOnHoldSuccess state,
  ) {
    final result = handleReloadDataWhenBack(
        ticketReloadDataActionEnum: TicketReloadDataActionEnum.rejectOnHold);
    Navigator.of(context).pop(result);
    Popup.instance.showSnackBar(
      message: LocaleKeys.ticket_details_reject_on_hold_success.tr,
      type: SnackbarType.success,
    );
  }

  void handleTicketCancelOnHoldSuccess(
    BuildContext context,
    TicketCancelOnHoldSuccess state,
  ) {
    loadDetails();
    widget.detailsData.ticketReloadDataActionEnum =
        TicketReloadDataActionEnum.continueHandle;
  }

  void handleTicketUnfollowSuccess(
    BuildContext context,
    TicketUnfollowTicketSuccess state,
  ) {
    if (state.option == UnfollowOption.normal) {
      final result = handleReloadDataWhenBack(
          ticketReloadDataActionEnum: TicketReloadDataActionEnum.unfollow);
      Navigator.of(context).pop(result);
    }
    Popup.instance.showSnackBar(
      message: LocaleKeys.ticket_details_unfollow_success.tr,
      type: SnackbarType.success,
    );
  }

  void handleTicketDeleteTicketSuccess(
    BuildContext context,
    TicketDeleteTicketSuccess state,
  ) {
    final result = handleReloadDataWhenBack(
        ticketReloadDataActionEnum: TicketReloadDataActionEnum.delete);
    Navigator.of(context).pop(result);
    Popup.instance.showSnackBar(
      message: LocaleKeys.ticket_details_delete_success.tr,
      type: SnackbarType.success,
    );
  }

  void handleTicketConfirmCloseTicketSuccess(
    BuildContext context,
    TicketConfirmCloseTicketSuccess state,
  ) {
    final result = handleReloadDataWhenBack(
        ticketReloadDataActionEnum: TicketReloadDataActionEnum.close);
    Navigator.of(context).pop(result);
    Popup.instance.showSnackBar(
      message: LocaleKeys.ticket_details_close_success.tr,
      type: SnackbarType.success,
    );
  }

  void handleTicketConfirmReopenTicketSuccess(
    BuildContext context,
    TicketReopenTicketSuccess state,
  ) {
    final result = handleReloadDataWhenBack(
        ticketReloadDataActionEnum: TicketReloadDataActionEnum.reopen);
    Navigator.of(context).pop(result);
    Popup.instance.showSnackBar(
      message: LocaleKeys.ticket_details_reopen_success.tr,
      type: SnackbarType.success,
    );
  }

  void handleTicketReviewTicketSuccess(
    BuildContext context,
    TicketReviewTicketSuccess state,
  ) {
    bloc.add(
      TicketCloseTicketEvent(
        ticketEntity: widget.detailsData.ticketEntity!,
        hasReview: true,
      ),
    );
  }

  void handleTicketCancelSuccess(
    BuildContext context,
    TicketCancelTicketSuccess state,
  ) {
    final result = handleReloadDataWhenBack(
        ticketReloadDataActionEnum: TicketReloadDataActionEnum.cancel);
    Navigator.of(context).pop(result);
    Popup.instance.showSnackBar(
      message: LocaleKeys.ticket_details_cancel_success.tr,
      type: SnackbarType.success,
    );
  }

  void handleTicketAddLabelSuccess(
    BuildContext context,
    TicketAddLabelSuccess state,
  ) {
    loadDetails();
    widget.detailsData.ticketReloadDataActionEnum =
        TicketReloadDataActionEnum.addLabels;
    Popup.instance.showSnackBar(
      message: LocaleKeys.ticket_details_add_label_success.tr,
      type: SnackbarType.success,
    );
  }

  void handleTicketAddHandlerSuccess(
      BuildContext context, TicketAddHandlerSuccess state) {
    Popup.instance.showSnackBar(
      message: LocaleKeys.ticket_details_add_handler_success.tr,
      type: SnackbarType.success,
    );
  }

  void handleTicketChangeHandlerSuccess(
      BuildContext context, TicketChangeHandlerSuccess state) {
    Popup.instance.showSnackBar(
      message: LocaleKeys.ticket_details_change_handler_success.tr,
      type: SnackbarType.success,
    );
  }

  void handleTicketAddFollowerSuccess(
      BuildContext context, TicketAddFollowerSuccess state) {
    if (state.option == ManageFollowerOption.addAllSteps) {
      Popup.instance.showSnackBar(
        message: LocaleKeys.ticket_details_add_follower_success_all_step.tr,
        type: SnackbarType.success,
      );
    } else {
      Popup.instance.showSnackBar(
        message: LocaleKeys.ticket_details_add_follower_success.tr,
        type: SnackbarType.success,
      );
    }
    loadDetails();
    widget.detailsData.ticketReloadDataActionEnum =
        TicketReloadDataActionEnum.addFollowers;
  }

  void handleTicketBackStepSuccess(
      BuildContext context, TicketBackStepSuccess state) {
    final result = handleReloadDataWhenBack(
        ticketReloadDataActionEnum: TicketReloadDataActionEnum.backStep);
    Navigator.of(context).pop(result);
    Popup.instance.showSnackBar(
      message: LocaleKeys.ticket_details_back_step_popup_back_success.tr,
      type: SnackbarType.success,
    );
  }

  void handleTicketBackEndStepSuccess(
      BuildContext context, TicketBackEndStepSuccess state) {
    final result = handleReloadDataWhenBack(
        ticketReloadDataActionEnum: TicketReloadDataActionEnum.backEndStep);
    Navigator.of(context).pop(result);
    Popup.instance.showSnackBar(
      message: LocaleKeys.ticket_details_back_step_popup_back_success.tr,
      type: SnackbarType.success,
    );
  }

  void handleTicketState(BuildContext context, TicketDetailsTabState state) {
    if (state is TicketDetailsLoaded) {
      widget.detailsData.ticketEntity = state.entity;
      handleTicketLoaded(state.entity);

      state.callback?.call(state.entity);
    } else if (state is TicketDetailsFlowChartLoaded) {
      assert(widget.detailsData.ticketEntity != null);

      if (widget.detailsData.ticketEntity != null) {
        handleTicketFlowChartLoaded(
          widget.detailsData.ticketEntity!,
          state.entity,
        );
      }
    } else if (state is TicketDetailsNodeLoaded) {
      handleTicketDetailsNodeLoaded(
        ticketEntity: state.ticketEntity,
        flowChartEntity: state.ticketFlowChartEntity,
        nodeEntity: state.ticketNodeEntity,
      );
    } else if (state is TicketGetIsTicketAssigneeSuccess) {
      handleTicketGetIsTicketAssigneeSuccess(context, state);
    } else if (state is TicketGetActionPermissionsSuccess) {
      handleTicketGetActionPermissionsSuccess(context, state);
    } else if (state is TicketGetAssigneesSuccess) {
      handleTicketGetAssigneesSuccess(context, state);
    } else if (state is TicketGetAdminWorkflowSuccess) {
      handleTicketGetAdminWorkflowSuccess(context, state);
    }

    /// ticket action
    else if (state is TicketAdditionalRequestSuccess) {
      handleTicketAdditionalRequestSuccess(context, state);
    } else if (state is TicketChangeNodeStatusSuccess) {
      handleTicketChangeNodeStatusSuccess(context, state);
    } else if (state is TicketSpamSuccess) {
      handleTicketSpamSuccess(context, state);
    } else if (state is TicketMoveToOnHoldSuccess) {
      handleTicketMoveToOnHoldSuccess(context, state);
    } else if (state is TicketGetOnHoldSuccess) {
      handleTicketGetOnHoldSuccess(context, state);
    } else if (state is TicketAcceptOnHoldSuccess) {
      handleTicketAcceptOnHoldSuccess(context, state);
    } else if (state is TicketRejectOnHoldSuccess) {
      handleTicketRejectOnHoldSuccess(context, state);
    } else if (state is TicketCancelOnHoldSuccess) {
      handleTicketCancelOnHoldSuccess(context, state);
    } else if (state is TicketGetAdditionalRequestsSuccess) {
      handleTicketGetAdditionalRequestsSuccess(context, state);
    } else if (state is TicketUnfollowTicketSuccess) {
      handleTicketUnfollowSuccess(context, state);
    } else if (state is TicketDeleteTicketSuccess) {
      handleTicketDeleteTicketSuccess(context, state);
    } else if (state is TicketConfirmCloseTicketSuccess) {
      handleTicketConfirmCloseTicketSuccess(context, state);
    } else if (state is TicketReopenTicketSuccess) {
      handleTicketConfirmReopenTicketSuccess(context, state);
    } else if (state is TicketReviewTicketSuccess) {
      handleTicketReviewTicketSuccess(context, state);
    } else if (state is TicketCancelTicketSuccess) {
      handleTicketCancelSuccess(context, state);
    } else if (state is TicketAddLabelSuccess) {
      handleTicketAddLabelSuccess(context, state);
    } else if (state is TicketAddHandlerSuccess) {
      handleTicketAddHandlerSuccess(context, state);
    } else if (state is TicketChangeHandlerSuccess) {
      handleTicketChangeHandlerSuccess(context, state);
    } else if (state is TicketAddFollowerSuccess) {
      handleTicketAddFollowerSuccess(context, state);
    } else if (state is TicketBackStepSuccess) {
      handleTicketBackStepSuccess(context, state);
    } else if (state is TicketBackEndStepSuccess) {
      handleTicketBackEndStepSuccess(context, state);
    }
  }

  void reloadTicketDetailsPage() {
    widget.ticketDetailsBloc?.add(
      TicketInitTabEvent(
        ticketEntity: widget.detailsData.ticketEntity!,
        flowChartEntity: widget.detailsData.flowChartEntity,
        nodeEntity: widget.detailsData.nodeEntity,
        ticketActionEntity: widget.detailsData.ticketActionEntity,
      ),
    );
  }

  void onTicketActionClick(
    BuildContext context,
    TicketActionBottomSheetEntity actionEntity,
    TicketActionResult actionResult,
  ) {
    if (actionResult.action == TicketAction.addHandler ||
        actionResult.action == TicketAction.changeHandler) {
      assert(actionResult.result is List<AssigneeEntity>);
      if (actionResult.result is List<AssigneeEntity> &&
          actionResult.result.isNotEmpty) {
        bloc.add(
          TicketActionAssigneeEvent(
            ticketId: actionEntity.ticketEntity.id.toString(),
            nodeId: actionEntity.ticketNodeEntity.id.toString(),
            assigneeId: actionResult.result.first.id,
            action: actionResult.action,
            currentAssigneeId: actionEntity.ticketNodeEntity.assignee?.id,
          ),
        );

        widget.detailsData.ticketReloadDataActionEnum =
            TicketReloadDataActionEnum.addHandler;
      }
    } else if (actionResult.action == TicketAction.manageFollower) {
      if (actionResult.result is List) {
        final result = actionResult.result as List;
        if (result.isEmpty) return;
        assert(result.first is TicketFollowerParams);
        assert(result[1] is ManageFollowerOption);

        final params = result.first as TicketFollowerParams;
        final option = result[1] as ManageFollowerOption;

        if (params.addAssignees.isNotEmpty ||
            params.removeAssignees.isNotEmpty) {
          bloc.add(
            TicketActionAddFollowerEvent(
              ticketId: actionEntity.ticketEntity.id.toString(),
              nodeId: actionEntity.ticketNodeEntity.id.toString(),
              params: params,
              option: option,
            ),
          );
        }
      }
    } else if (actionResult.action == TicketAction.requestToProvideInfo) {
      if (actionResult.result is List) {
        final result = actionResult.result as List;

        if (result.isEmpty) return;
        assert(result.first is TicketActionBottomSheetEntity);
        assert(result[1] is String);

        final TicketActionBottomSheetEntity entity = result.first;

        bloc.add(
          TicketAdditionalRequestsEvent(
            params: TicketAdditionalRequestsParams(
              content: result[1],
              nodeId: entity.ticketNodeEntity.id,
              ticketId: entity.ticketEntity.id,
              targetId: entity.ticketEntity.createdBy ?? -1,
            ),
          ),
        );
      }
    } else if (actionResult.action == TicketAction.editField) {
      if (actionResult.result is List) {
        final result = actionResult.result as List;

        if (result.isEmpty) return;

        assert(result.first is TicketActionBottomSheetEntity);
        assert(result[1] is TicketActionResultType);

        if (result[1] == TicketActionResultType.reloadData) {
          // final TicketActionBottomSheetEntity entity = result.first;
          loadDetails();
          widget.detailsData.ticketReloadDataActionEnum =
              TicketReloadDataActionEnum.editField;
        }
      }
    } else if (actionResult.action == TicketAction.duplicate) {
      if (actionResult.result is List) {
        final result = actionResult.result as List;

        if (result.isEmpty) return;
        // if (result.last == TicketActionResultType.doNothing) return;

        Future.delayed(const Duration(milliseconds: 100)).then((value) {
          widget.hideLoading();

          final reloadDataResult = handleReloadDataWhenBack(
            ticketReloadDataActionEnum: TicketReloadDataActionEnum.duplicate,
          );
          Navigator.of(context).pop(reloadDataResult);

          Popup.instance.showSnackBar(
            message: LocaleKeys.ticket_details_duplicate_snackbar_success.tr,
            type: SnackbarType.success,
          );
        });

        // chờ 1 xíu trước khi pop để tránh lỗi
        // Future.delayed(const Duration(milliseconds: 100)).then((value) {
        //   widget.hideLoading();
        //   Navigator.pop(context);
        // });
      }
    } else if (actionResult.action == TicketAction.nextStep) {
      if (actionResult.result is TicketActionBottomSheetEntity) {
        final validateRequireData = hasDataRequireFields(
            ticketEntity: widget.detailsData.ticketEntity,
            permissions: nodeFieldPermissions);
        if (validateRequireData) {
          final result = actionResult.result as TicketActionBottomSheetEntity;

          bloc.add(
            TicketChangeNodeStatusEvent(
              ticketEntity: result.ticketEntity,
              ticketNodeEntity: result.ticketNodeEntity,
              // TicketAction.nextStep chỉ có nodeStatus là:
              // TicketNodeStatus.handling
              nodeStatus: TicketNodeStatus.handled,
            ),
          );
        } else {
          Popup.instance.showSnackBar(
            message: LocaleKeys.ticket_details_fill_require_fields_error.tr,
            type: SnackbarType.error,
          );
        }
      }
    } else if (actionResult.action == TicketAction.continueHandle) {
      if (actionResult.result is TicketActionBottomSheetEntity) {
        final result = actionResult.result as TicketActionBottomSheetEntity;

        if (widget.detailsData.ticketOnHoldRequestEntity != null) {
          bloc.add(
            TicketCancelOnHoldRequestEvent(
              ticketEntity: result.ticketEntity,
              ticketNodeEntity: result.ticketNodeEntity,
              ticketOnHoldRequestEntity:
                  widget.detailsData.ticketOnHoldRequestEntity!,
            ),
          );
        } else {
          bloc.add(
            TicketChangeNodeStatusEvent(
              ticketEntity: result.ticketEntity,
              ticketNodeEntity: result.ticketNodeEntity,
              // TicketAction.continueHandle chỉ có nodeStatus là:
              // TicketNodeStatus.handling
              nodeStatus: TicketNodeStatus.handling,
            ),
          );
        }
      }
    } else if (actionResult.action == TicketAction.approve) {
      if (actionResult.result is TicketActionBottomSheetEntity) {
        final validateRequireData = hasDataRequireFields(
            ticketEntity: widget.detailsData.ticketEntity,
            permissions: nodeFieldPermissions);
        if (validateRequireData) {
          final result = actionResult.result as TicketActionBottomSheetEntity;

          bloc.add(
            TicketChangeNodeStatusEvent(
              ticketEntity: result.ticketEntity,
              ticketNodeEntity: result.ticketNodeEntity,
              // TicketAction.approve chỉ có nodeStatus là:
              // TicketNodeStatus.approved
              nodeStatus: TicketNodeStatus.approved,
            ),
          );
        } else {
          Popup.instance.showSnackBar(
            message: LocaleKeys.ticket_details_fill_require_fields_error.tr,
            type: SnackbarType.error,
          );
        }
      }
    } else if (actionResult.action == TicketAction.reject) {
      if (actionResult.result is TicketActionBottomSheetEntity) {
        final validateRequireData = hasDataRequireFields(
            ticketEntity: widget.detailsData.ticketEntity,
            permissions: nodeFieldPermissions);
        if (validateRequireData) {
          final result = actionResult.result as TicketActionBottomSheetEntity;

          bloc.add(
            TicketChangeNodeStatusEvent(
              ticketEntity: result.ticketEntity,
              ticketNodeEntity: result.ticketNodeEntity,
              // TicketAction.reject chỉ có nodeStatus là:
              // TicketNodeStatus.notApprove
              nodeStatus: TicketNodeStatus.notApprove,
            ),
          );
        } else {
          Popup.instance.showSnackBar(
            message: LocaleKeys.ticket_details_fill_require_fields_error.tr,
            type: SnackbarType.error,
          );
        }
      }
    } else if (actionResult.action == TicketAction.duplicate) {
      // doNothing
    } else if (actionResult.action == TicketAction.moveToOnHold) {
      if (actionResult.result is List) {
        final result = actionResult.result as List;

        if (result.isEmpty) return;
        assert(result.first is TicketActionBottomSheetEntity);
        assert(result[1] is String);

        final TicketActionBottomSheetEntity entity = result.first;

        bloc.add(
          TicketMoveToOnHoldEvent(
            ticketEntity: entity.ticketEntity,
            ticketNodeEntity: entity.ticketNodeEntity,
            params: TicketMoveToOnHoldParams(
              reason: result[1],
            ),
          ),
        );
      }
    } else if (actionResult.action == TicketAction.spam) {
      if (actionResult.result is List) {
        final result = actionResult.result as List;

        if (result.isEmpty) return;
        assert(result.first is TicketActionBottomSheetEntity);
        assert(result[1] is String);

        final TicketActionBottomSheetEntity entity = result.first;

        bloc.add(
          TicketSpamEvent(
            ticketEntity: entity.ticketEntity,
            ticketNodeEntity: entity.ticketNodeEntity,
            params: TicketSpamParams(
              reason: result[1],
            ),
          ),
        );
      }
    } else if (actionResult.action == TicketAction.approveOnHold) {
      if (actionResult.result is TicketActionBottomSheetEntity) {
        final result = actionResult.result as TicketActionBottomSheetEntity;

        assert(widget.detailsData.ticketOnHoldRequestEntity != null);
        if (widget.detailsData.ticketOnHoldRequestEntity != null) {
          bloc.add(
            TicketAcceptOnHoldRequestEvent(
              ticketEntity: result.ticketEntity,
              ticketNodeEntity: result.ticketNodeEntity,
              ticketOnHoldRequestEntity:
                  widget.detailsData.ticketOnHoldRequestEntity!,
            ),
          );
        } else {
          // TODO:
        }
      }
    } else if (actionResult.action == TicketAction.rejectOnHold) {
      if (actionResult.result is TicketActionBottomSheetEntity) {
        final result = actionResult.result as TicketActionBottomSheetEntity;

        assert(widget.detailsData.ticketOnHoldRequestEntity != null);
        if (widget.detailsData.ticketOnHoldRequestEntity != null) {
          bloc.add(
            TicketRejectOnHoldRequestEvent(
              ticketEntity: result.ticketEntity,
              ticketNodeEntity: result.ticketNodeEntity,
              ticketOnHoldRequestEntity:
                  widget.detailsData.ticketOnHoldRequestEntity!,
            ),
          );
        } else {
          // TODO:
        }
      }
      // } else if (actionResult.action == TicketAction.unfollow) {
      //   if (actionResult.result is List) {
      //     final result = actionResult.result as List;

      //     if (result.isEmpty) return;
      //     assert(result.first is UnfollowOption);
      //     assert(result[1] is TicketActionBottomSheetEntity);

      //     final TicketActionBottomSheetEntity entity = result[1];
      //     final option = result[1] as UnfollowOption;

      //     if (option == UnfollowOption.normal) {
      //       bloc.add(
      //         TicketUnfollowTicketEvent(
      //           ticketEntity: entity.ticketEntity,
      //           ticketNodeEntity: entity.ticketNodeEntity,
      //           option: option,
      //         ),
      //       );
      //     } else {
      //       // TODO:
      //     }
      //   }
    } else if (actionResult.action == TicketAction.deleteTicket) {
      if (actionResult.result is TicketActionBottomSheetEntity) {
        final result = actionResult.result as TicketActionBottomSheetEntity;

        bloc.add(
          TicketDeleteTicketEvent(
            ticketEntity: result.ticketEntity,
          ),
        );
      }
    } else if (actionResult.action == TicketAction.reopenTicket) {
      if (actionResult.result is List) {
        final result = actionResult.result as List;

        if (result.isEmpty) return;
        assert(result.first is TicketActionBottomSheetEntity);
        assert(result[1] is String);

        final TicketActionBottomSheetEntity entity = result.first;

        bloc.add(
          TicketReopenTicketEvent(
            ticketEntity: entity.ticketEntity,
            params: TicketReopenParams(
              reason: result[1],
            ),
          ),
        );
      }
    } else if (actionResult.action == TicketAction.closeTicket) {
      if (actionResult.result is TicketActionBottomSheetEntity) {
        final result = actionResult.result as TicketActionBottomSheetEntity;

        bloc.add(
          TicketCloseTicketEvent(
            ticketEntity: result.ticketEntity,
          ),
        );
      }
    } else if (actionResult.action == TicketAction.cancelTicket) {
      if (actionResult.result is TicketActionBottomSheetEntity) {
        final result = actionResult.result as TicketActionBottomSheetEntity;

        bloc.add(
          TicketCancelEvent(
            ticketEntity: result.ticketEntity,
            params: TicketCancelParams(
              ticketNodeId: result.ticketNodeEntity.id,
            ),
          ),
        );
      }
    } else if (actionResult.action == TicketAction.addLabel) {
      if (actionResult.result is List) {
        final result = actionResult.result as List;
        if (result.isEmpty) return;
        assert(result.first is TicketActionBottomSheetEntity);
        assert(result[1] is List<WorkflowTagEntity>);

        final TicketActionBottomSheetEntity entity = result.first;
        final ticketTags = result[1] as List<WorkflowTagEntity>;

        bloc.add(
          TicketAddLabelEvent(
            ticketEntity: entity.ticketEntity,
            ticketNodeEntity: entity.ticketNodeEntity,
            params: TicketLabelParams(
              tagIds:
                  ticketTags.map((e) => int.parse(e.id.toString())).toList(),
            ),
          ),
        );
      }
    } else if (actionResult.action == TicketAction.backStep) {
      if (actionResult.result is List) {
        final result = actionResult.result as List;
        if (result.isEmpty) return;
        assert(result.first is TicketActionBottomSheetEntity);

        if (result[1] is BackStepResult) {
          final TicketActionBottomSheetEntity entity = result.first;
          final backStepResult = result[1] as BackStepResult;

          bloc.add(
            TicketBackStepEvent(
              ticketEntity: entity.ticketEntity,
              ticketNodeEntity: entity.ticketNodeEntity,
              reason: backStepResult.reason,
              nodeIds: backStepResult.nodeIds,
            ),
          );
        }
      }
    } else if (actionResult.action == TicketAction.backEndStep) {
      if (actionResult.result is List) {
        final result = actionResult.result as List;
        if (result.isEmpty) return;
        assert(result.first is TicketActionBottomSheetEntity);

        if (result[1] is BackStepResult) {
          final TicketActionBottomSheetEntity entity = result.first;
          final backEndStepResult = result[1] as BackStepResult;

          bloc.add(
            TicketBackEndStepEvent(
              ticketEntity: entity.ticketEntity,
              reason: backEndStepResult.reason,
              nodeIds: backEndStepResult.nodeIds,
            ),
          );
        }
      }
    }

    logDebug('onTicketActionClick -> ${actionResult.action}');
  }

  void handleTicketLoaded(TicketEntity ticketEntity) {
    bloc.add(
      TicketFlowChartEvent(
        ticketId: ticketEntity.id.toString(),
      ),
    );

    // widget.hideLoading();
  }

  void loadDetails({int milliseconds = 100}) {
    widget.showLoading();

    Future.delayed(Duration(milliseconds: milliseconds)).then((v) {
      bloc.add(
        TicketGetDetailEvent(
          id: widget.detailsData.ticketEntity?.id.toString() ?? '',
        ),
      );
    });
  }

  void onSubmitRating(String rating, int point) {
    widget.showLoading();

    bloc.add(
      TicketReviewEvent(
        ticketEntity: widget.detailsData.ticketEntity!,
        params: TicketReviewParams(
          ratingPoint: point,
          review: rating,
        ),
      ),
    );
  }

  void onRatingReopen() async {
    final result = await GetIt.I<TicketNavigator>()
        .showBottomSheet(child: DetailsReopenWidget());

    if (result is String) {
      bloc.add(
        TicketReopenTicketEvent(
          ticketEntity: widget.detailsData.ticketEntity!,
          params: TicketReopenParams(
            reason: result,
          ),
        ),
      );
    }
  }

  @override
  void initState() {
    super.initState();

    /// chỉ init data 1 lần duy nhất
    if (widget.detailsData.ticketEntity == null) {
      bloc.add(TicketGetDetailEvent(id: widget.detailsData.ticketId));
    }
  }

  @override
  void didUpdateWidget(covariant TicketDetailsTabWrapperWidget oldWidget) {
    if (oldWidget.detailsData.nodeEntity?.id !=
        widget.detailsData.nodeEntity?.id) {
      widget.showLoading();
      handleTicketDetailsNodeLoaded(
          ticketEntity: widget.detailsData.ticketEntity!,
          flowChartEntity: widget.detailsData.flowChartEntity!,
          nodeEntity: widget.detailsData.nodeEntity!);
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return BlocConsumer<TicketDetailsTabBloc, TicketDetailsTabState>(
      bloc: bloc,
      listener: (context, state) {
        handleTicketState(context, state);
      },
      // buildWhen: (previous, current) {
      //   return current is TicketDetailsLoaded;
      // },
      builder: (context, state) {
        if (widget.detailsData.ticketEntity == null ||
            widget.detailsData.nodeEntity == null) {
          return const SizedBox();
        }

        final actionWidget = widget.detailsData.flowChartEntity != null &&
                widget.detailsData.nodeEntity != null
            ? HomeActionView(
                ticketEntity: widget.detailsData.ticketEntity!,
                ticketFlowChartEntity: widget.detailsData.flowChartEntity!,
                ticketNodeEntity: widget.detailsData.nodeEntity!,
                ticketActionEntity: widget.detailsData.ticketActionEntity,
                onTicketActionClick: onTicketActionClick,
                assignees: widget.detailsData.ticketAssignees,
                permissions: nodeFieldPermissions,
              )
            : const SizedBox();

        return Column(
          children: [
            Expanded(
              child: _TicketDetailsTabWidget(
                entity: widget.detailsData.ticketEntity!,
                ticketNodeEntity: widget.detailsData.nodeEntity!,
                formFieldPermissions: nodeFieldPermissions,
                ticketAdditionalRequestEntities:
                    widget.detailsData.ticketAdditionalRequestEntities,
                onUpdateData: (ticketEntity, ticketAdditionalRequestEntities) {
                  // update widget
                  // widget.detailsData.ticketEntity = ticketEntity;
                  // widget.detailsData.ticketAdditionalRequestEntities =
                  //     ticketAdditionalRequestEntities;
                  loadDetails();
                },
                onSubmitRating: onSubmitRating,
                onRatingReopen: onRatingReopen,
              ),
            ),
            Divider(
              color: GPColor.lineTertiary,
              height: 1,
              thickness: 1,
            ),
            actionWidget,
          ],
        );
      },
    );
  }
}

/// tab detail của 1 widget TicketDetailsTabBloc
class _TicketDetailsTabWidget extends StatelessWidget {
  const _TicketDetailsTabWidget({
    required this.entity,
    required this.formFieldPermissions,
    required this.onUpdateData,
    this.ticketAdditionalRequestEntities,
    required this.onSubmitRating,
    required this.onRatingReopen,
    required this.ticketNodeEntity,
  });

  final TicketEntity entity;
  final TicketNodeEntity ticketNodeEntity;
  final List<WorkflowFormFieldPermissionEntity> formFieldPermissions;
  final List<TicketAdditionalRequestEntity>? ticketAdditionalRequestEntities;
  final TicketDetailsOnUpdateData onUpdateData;
  final Function(String, int) onSubmitRating;
  final Function() onRatingReopen;

  void onSuccess(
    TicketEntity? ticketEntity,
    TicketAdditionalRequestEntity? ticketAdditionalRequestEntity,
  ) {
    // ---------- user cập nhật phản hồi thông tin bổ sung ----------
    if (ticketAdditionalRequestEntity != null) {
      if (ticketAdditionalRequestEntities == null) return;

      final updateEntity = ticketAdditionalRequestEntities
          ?.firstWhereOrNull((e) => e.id == ticketAdditionalRequestEntity.id);
      if (updateEntity == null) return;

      final index = ticketAdditionalRequestEntities?.indexOf(updateEntity);

      if (index == null) return;

      ticketAdditionalRequestEntities![index] = ticketAdditionalRequestEntity;
    }

    // ---------- user cập nhật thông tin ticket ----------
    if (ticketEntity != null) {
      // entity = ticketEntity;
      onUpdateData(ticketEntity, ticketAdditionalRequestEntities);
    } else {
      onUpdateData(null, ticketAdditionalRequestEntities);
    }
  }

  @override
  Widget build(BuildContext context) {
    final needResponses =
        ticketAdditionalRequestEntities?.needResponses() ?? [];
    return SingleChildScrollView(
      controller: ticketDetailsScrollController,
      child: Column(
        children: [
          _TitleWidget(entity: entity),
          if (ticketAdditionalRequestEntities != null) ...{
            needResponses.isEmpty || !entity.isRequester
                ? const SizedBox()
                : Container(
                    margin: const EdgeInsets.only(top: 12),
                    color: GPColor.bgPrimary,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(
                            vertical: 12,
                            horizontal: 16,
                          ),
                          child: Text(
                            LocaleKeys.ticket_details_provide_info_title.tr,
                            style: textStyle(GPTypography.headingMedium),
                          ),
                        ),
                        Divider(
                          color: GPColor.lineSecondary,
                          height: 1,
                        ),
                        ...needResponses.map(
                          (e) {
                            final bool isLast = e.id == needResponses.last.id;
                            return Column(
                              children: [
                                TicketRequestProvideInfoWidget(
                                  ticketEntity: entity,
                                  ticketNodeEntity: ticketNodeEntity,
                                  ticketAdditionalRequestEntity: e,
                                  canResponse: e.canResponse,
                                  onSuccess: onSuccess,
                                  fieldPermissions: formFieldPermissions,
                                ),
                                if (!isLast)
                                  Divider(
                                    color: GPColor.lineSecondary,
                                    height: 1,
                                  ).paddingAll(12),
                              ],
                            );
                          },
                        ),
                      ],
                    ),
                  )
          },
          _TicketReviewQualityWidget(
            entity: entity,
            onSubmit: onSubmitRating,
            onReopen: onRatingReopen,
          ),
          if (entity.hasReview) _TicketViewReviewWidget(entity: entity),
          _TicketFormWidget(
              entity: entity, fieldPermissions: formFieldPermissions),
          _ResponsedWidget(
            ticketEntity: entity,
            ticketAdditionalRequestEntities: ticketAdditionalRequestEntities,
            onSuccess: onSuccess,
            fieldPermissions: formFieldPermissions,
            ticketNodeEntity: ticketNodeEntity,
          ),
          _RelativeTicketWidget(entity: entity),
        ],
      ),
    );
  }
}

class _TitleWidget extends StatelessWidget {
  const _TitleWidget({
    required this.entity,
  });

  final TicketEntity entity;

  void _onCopyTicketCodeTap(String code) {
    Clipboard.setData(ClipboardData(text: code)).then((_) {
      Popup.instance.showSnackBar(
          message: LocaleKeys.ticket_details_detail_tab_code_copied.tr,
          type: SnackbarType.success);
    });
  }

  @override
  Widget build(BuildContext context) {
    return TicketDetailsContainer(
      margin: const EdgeInsets.only(top: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              entity.status.toWidget(),
              const SizedBox(width: 8),
              Text(entity.createdAtStr),
              const SizedBox(width: 8),
              if (entity.isPrivate == true) ...{
                SvgWidget(
                  Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC12_FILL_DOT_POINT_SVG,
                  color: GPColor.contentTertiary,
                ),
                const SizedBox(width: 8),
                SvgWidget(
                  Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC16_FILL_LOCK_SVG,
                  color: GPColor.contentSecondary,
                ),
              },
            ],
          ),
          const SizedBox(height: 8),
          Text(
            entity.title,
            style: textStyle(GPTypography.headingLarge),
          ),
          const SizedBox(height: 20),
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: '${LocaleKeys.ticket_details_detail_tab_type.tr}:',
                  style: textStyle(GPTypography.bodyMedium)
                      ?.mergeColor(GPColor.contentSecondary),
                ),
                const WidgetSpan(
                  child: SizedBox(width: 8),
                ),
                TextSpan(
                  text: entity.workflow?.name ?? '',
                  style: textStyle(GPTypography.bodyMedium),
                )
              ],
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Flexible(
                child: RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text:
                            '${LocaleKeys.ticket_details_detail_tab_code.tr}:',
                        style: textStyle(GPTypography.bodyMedium)
                            ?.mergeColor(GPColor.contentSecondary),
                      ),
                      const WidgetSpan(
                        child: SizedBox(width: 8),
                      ),
                      TextSpan(
                        text: entity.displayCode,
                        style: textStyle(GPTypography.bodyMedium),
                      )
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 8),
              InkWell(
                onTap: () => _onCopyTicketCodeTap(entity.code),
                child: SvgWidget(
                  Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_2SQUARE_SVG,
                  color: GPColor.contentSecondary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                '${LocaleKeys.ticket_details_detail_tab_priority.tr}:',
                style: textStyle(GPTypography.bodyMedium)
                    ?.mergeColor(GPColor.contentSecondary),
              ),
              const SizedBox(width: 8),
              entity.priority.toTextWidget(),
            ],
          )
        ],
      ),
    );
  }
}

class _TicketFormWidget extends StatelessWidget implements InputBehavior {
  _TicketFormWidget({
    required this.entity,
    required this.fieldPermissions,
  });

  final TicketEntity entity;
  final List<WorkflowFormFieldPermissionEntity> fieldPermissions;

  @override
  List<InputController> inputControllers = [];

  @override
  void onFocus() {
    // doNothing
  }

  @override
  Widget build(BuildContext context) {
    return TicketDetailsContainer(
      margin: const EdgeInsets.only(top: 8),
      padding: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            child: Text(
              LocaleKeys.ticket_details_detail_tab_ticket_content.tr,
              style: textStyle(GPTypography.headingMedium),
            ),
          ),
          Divider(color: GPColor.lineSecondary),
          // hiển thị thông tin details
          SingleChildScrollView(
            child: TicketDynamicFormDetailsPage(
              inputBehavior: this,
              fields: entity.fieldValues ?? [],
              allFields: entity.workflow?.form?.fields,
              formFieldPermissions: fieldPermissions,
            ),
          ),
        ],
      ),
    );
  }
}

class _ResponsedWidget extends StatelessWidget {
  const _ResponsedWidget({
    required this.ticketEntity,
    required this.onSuccess,
    this.ticketAdditionalRequestEntities,
    this.fieldPermissions,
    required this.ticketNodeEntity,
  });

  final TicketEntity ticketEntity;

  final List<TicketAdditionalRequestEntity>? ticketAdditionalRequestEntities;

  final TicketRequestProvideInfoSuccessCallBack onSuccess;

  final List<WorkflowFormFieldPermissionEntity>? fieldPermissions;

  final TicketNodeEntity ticketNodeEntity;

  @override
  Widget build(BuildContext context) {
    if (ticketAdditionalRequestEntities == null ||
        ticketAdditionalRequestEntities?.isEmpty == true) {
      return const SizedBox();
    }

    final responseds = ticketAdditionalRequestEntities?.responseds() ?? [];

    return Column(
      children: responseds.map(
        (e) {
          return Container(
            margin: const EdgeInsets.only(top: 12),
            color: GPColor.bgPrimary,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 12,
                    horizontal: 16,
                  ),
                  child: Text(
                    LocaleKeys.ticket_details_detail_tab_provided_content.tr,
                    style: textStyle(GPTypography.headingMedium),
                  ),
                ),
                Divider(
                  color: GPColor.lineSecondary,
                  height: 1,
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Yêu cầu bổ sung thông tin
                    TicketRequestProvideInfoWidget(
                      ticketEntity: ticketEntity,
                      ticketAdditionalRequestEntity: e,
                      canResponse: false,
                      onSuccess: onSuccess,
                      ticketNodeEntity: ticketNodeEntity,
                    ),
                    Divider(
                      color: GPColor.lineSecondary,
                      height: 1,
                    ),
                    const SizedBox(height: 16),
                    // Thông tin đã bổ sung
                    TicketRequestProvidedInfoWidget(
                      ticketEntity: ticketEntity,
                      ticketAdditionalRequestEntity: e,
                      onSuccess: onSuccess,
                      fieldPermissions: fieldPermissions,
                      ticketNodeEntity: ticketNodeEntity,
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ).toList(),
    );
  }
}

class _RelativeTicketWidget extends StatelessWidget {
  const _RelativeTicketWidget({
    required this.entity,
  });

  final TicketEntity entity;

  @override
  Widget build(BuildContext context) {
    final bool hasRefTickets =
        entity.refTickets != null && entity.refTickets?.isNotEmpty == true;

    if (!hasRefTickets) return const SizedBox();

    return TicketDetailsContainer(
      margin: const EdgeInsets.only(top: 8),
      padding: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            child: Text(
              LocaleKeys.ticket_details_detail_tab_ticket_relative.tr,
              style: textStyle(GPTypography.headingMedium),
            ),
          ),
          Divider(color: GPColor.lineSecondary),
          ListView.builder(
            shrinkWrap: true,
            physics: const ClampingScrollPhysics(),
            itemBuilder: (context, index) {
              final item = entity.refTickets![index];
              return _RelativeTicketItemWidget(entity: item);
            },
            itemCount: entity.refTickets?.length ?? 0,
          )
        ],
      ),
    );
  }
}

class _RelativeTicketItemWidget extends StatelessWidget {
  const _RelativeTicketItemWidget({
    required this.entity,
  });

  final RefTicketEntity entity;

  void goToDetails(BuildContext context) {
    // context.go(
    //   TicketDetailsRouteData(
    //     id: entity.id,
    //   ).location,
    //   extra: entity,
    // );

    /*
      context.go() khiến screen trước đó rebuild lại
      sẽ làm mất lastest state của listview, khiến listview hiển thiếu items
    */

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) {
          return TicketDetailsPage(
            id: entity.id.toString(),
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => goToDetails(context),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              entity.title,
              style: textStyle(GPTypography.bodyLarge),
              maxLines: 3,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                entity.status.toWidget(),
                const SizedBox(width: 8),
                Text(
                  '#${entity.code}',
                  maxLines: 3,
                  style: textStyle(GPTypography.bodyMedium)
                      ?.mergeColor(GPColor.contentSecondary),
                )
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: Row(
                    children: [
                      TicketAvatarWidget(
                        displayName: entity.createdByInfo?.displayName,
                        thumb: entity.createdByInfo?.avatarUrl,
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            entity.createdByInfo?.displayName ?? '',
                            maxLines: 1,
                            style: textStyle(GPTypography.bodyMedium)
                                ?.mergeColor(GPColor.contentPrimary),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            entity.createdAtStr,
                            maxLines: 1,
                            style: textStyle(GPTypography.bodyMedium)
                                ?.mergeColor(GPColor.contentSecondary),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}

class _TicketReviewQualityWidget extends StatefulWidget {
  const _TicketReviewQualityWidget({
    required this.entity,
    required this.onSubmit,
    required this.onReopen,
  });

  final TicketEntity entity;
  final Function(String, int) onSubmit;
  final Function() onReopen;

  @override
  State<_TicketReviewQualityWidget> createState() =>
      _TicketReviewQualityWidgetState();
}

class _TicketReviewQualityWidgetState
    extends State<_TicketReviewQualityWidget> {
  final TextEditingController _inputController = TextEditingController();
  final ValueNotifier<bool> rxHasRate = ValueNotifier<bool>(false);
  int rating = 0;

  @override
  Widget build(BuildContext context) {
    return widget.entity.showRating
        ? Padding(
            padding: const EdgeInsets.only(top: 8),
            child: DecoratedBox(
              decoration: BoxDecoration(
                color: GPColor.bgPrimary,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 16),
                    child: Text(
                      LocaleKeys.ticket_details_review_title.tr,
                      style: textStyle(GPTypography.headingMedium),
                    ),
                  ),
                  Divider(
                    color: GPColor.linePrimary,
                    height: 1,
                    thickness: 1,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        RatingStars(
                          onRatingChanged: (value) {
                            rxHasRate.value = true;
                            rating = value + 1;
                          },
                        ),
                        const SizedBox(height: 24),
                        TextFormField(
                          controller: _inputController,
                          maxLines: 3,
                          decoration: InputDecoration(
                            contentPadding: const EdgeInsets.symmetric(
                                vertical: 10, horizontal: 12),
                            hintText:
                                LocaleKeys.ticket_details_review_placeholder.tr,
                            hintStyle: textStyle(GPTypography.bodyMedium)
                                ?.copyWith(color: GPColor.contentTertiary),
                            border: OutlineInputBorder(
                              borderSide:
                                  BorderSide(color: GPColor.linePrimary),
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                        const SizedBox(height: 24),
                        ValueListenableBuilder(
                          valueListenable: rxHasRate,
                          builder: (context, value, child) {
                            return GPWorkButton(
                              isEnabled: rxHasRate.value,
                              title: LocaleKeys
                                  .ticket_details_review_submit_btn.tr,
                              onTap: () {
                                widget.onSubmit(_inputController.text, rating);
                              },
                            );
                          },
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              LocaleKeys.ticket_details_review_reopen_btn_p1.tr,
                              style: textStyle(GPTypography.bodyMedium)
                                  ?.copyWith(color: GPColor.contentSecondary),
                            ),
                            InkWell(
                              onTap: () {
                                widget.onReopen();
                              },
                              child: Text(
                                ' ${LocaleKeys.ticket_details_review_reopen_btn_p2.tr} ',
                                style: textStyle(GPTypography.headingSmall)
                                    ?.copyWith(
                                        color: GPColor
                                            .functionAccentWorkSecondary),
                              ),
                            ),
                            Text(
                              LocaleKeys.ticket_details_review_reopen_btn_p3.tr,
                              style: textStyle(GPTypography.bodyMedium)
                                  ?.copyWith(color: GPColor.contentSecondary),
                            ),
                          ],
                        )
                      ],
                    ),
                  )
                ],
              ),
            ),
          )
        : const SizedBox();
  }
}

class _TicketViewReviewWidget extends StatelessWidget {
  const _TicketViewReviewWidget({
    required this.entity,
  });

  final TicketEntity entity;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: GPColor.bgPrimary,
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                LocaleKeys.ticket_details_review_title.tr,
                style: textStyle(GPTypography.headingMedium),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  TicketAvatarWidget(
                    displayName: entity.creator?.displayName ?? '',
                    thumb: entity.creator?.avatarUrl,
                    size: 24,
                  ),
                  Flexible(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Text(
                        entity.creator?.displayName ?? '',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: textStyle(GPTypography.bodyMedium)?.copyWith(
                            color: GPColor.contentSecondary, height: 1.4),
                      ),
                    ),
                  ),
                  SvgWidget(
                    Assets
                        .PACKAGES_GP_ASSETS_IMAGES_SVG_IC12_FILL_DOT_POINT_SVG,
                    color: GPColor.contentTertiary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    entity.updatedAtStr,
                    style: textStyle(GPTypography.bodyMedium)?.copyWith(
                        color: GPColor.contentSecondary, height: 1.4),
                  )
                ],
              ),
              const SizedBox(height: 12),
              RatingStars(
                rating: entity.ratingPoint,
                isViewOnly: true,
                spacing: 12,
                starColor: GPColor.contentQuaternary,
              ),
              if (entity.review != null) ...{
                Divider(
                  color: GPColor.lineTertiary,
                  height: 32,
                  thickness: 1,
                ),
                Text(
                  entity.review ?? '',
                  style: textStyle(GPTypography.bodyLarge),
                )
              }
            ],
          ),
        ),
      ),
    );
  }
}
