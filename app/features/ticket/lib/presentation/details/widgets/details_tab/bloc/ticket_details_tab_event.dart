import 'package:gp_core/core.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_feat_ticket/data/model/request/ticket/details/cancel/ticket_cancel.params.dart';
import 'package:gp_feat_ticket/data/model/request/ticket/details/tag/ticket_tag.params.dart';
import 'package:gp_feat_ticket/domain/entity/enums/ticket/action/ticket_action.dart';
import 'package:gp_feat_ticket/domain/entity/enums/ticket/ticket_get_assignee.dart';
import 'package:gp_feat_ticket/domain/entity/enums/ticket/ticket_node_status.dart';
import 'package:gp_feat_ticket/domain/entity/enums/ticket/action/follower_option.dart';

import '../../../../../data/data.dart';
import '../../../../../domain/entity/ticket/ticket.dart';

final class TicketDetailsTabEvent extends CoreV2BaseEvent {
  const TicketDetailsTabEvent();
}

final class TicketGetDetailEvent extends TicketDetailsTabEvent {
  const TicketGetDetailEvent({
    required this.id,
  });

  final String id;
}

final class TicketFlowChartEvent extends TicketDetailsTabEvent {
  const TicketFlowChartEvent({
    required this.ticketId,
  });

  final String ticketId;
}

final class TicketNodeEvent extends TicketDetailsTabEvent {
  const TicketNodeEvent({
    required this.ticketEntity,
    required this.flowChartEntity,
    this.nodeId,
  });

  final TicketEntity ticketEntity;
  final TicketFlowChartEntity flowChartEntity;
  final String? nodeId;
}

final class TicketActionAssigneeEvent extends TicketDetailsTabEvent {
  const TicketActionAssigneeEvent({
    required this.ticketId,
    required this.nodeId,
    required this.assigneeId,
    required this.action,
    this.currentAssigneeId,
  });

  final String ticketId;
  final String nodeId;
  final int assigneeId;
  final int? currentAssigneeId;
  final TicketAction action;
}

final class TicketActionAddFollowerEvent extends TicketDetailsTabEvent {
  const TicketActionAddFollowerEvent({
    required this.ticketId,
    required this.nodeId,
    required this.params,
    required this.option,
  });

  final String ticketId;
  final String nodeId;
  final TicketFollowerParams params;
  final ManageFollowerOption option;
}

final class TicketAdditionalRequestsEvent extends TicketDetailsTabEvent {
  const TicketAdditionalRequestsEvent({
    required this.params,
  });

  final TicketAdditionalRequestsParams params;
}

final class TicketGetActionPermissionsEvent extends TicketDetailsTabEvent {
  const TicketGetActionPermissionsEvent({
    required this.ticketEntity,
    required this.ticketNodeEntity,
    required this.ticketFlowChartEntity,
    required this.isTicketAssignee,
    required this.adminWorkflow,
  });

  final TicketEntity ticketEntity;
  final TicketNodeEntity ticketNodeEntity;
  final TicketFlowChartEntity ticketFlowChartEntity;
  final bool isTicketAssignee;
  final List<Assignee> adminWorkflow;
}

final class TicketGetAssigneesEvent extends TicketDetailsTabEvent {
  const TicketGetAssigneesEvent({
    required this.assignees,
    required this.type,
  });

  final List<TicketAssigneeEntity>? assignees;
  final TicketGetAssigneeType type;
}

/// Dùng để update status cho các action
/// `TicketAction.nextStep`
/// `TicketAction.continueHandle`
/// `TicketAction.approve`
/// `TicketAction.reject`
final class TicketChangeNodeStatusEvent extends TicketDetailsTabEvent {
  const TicketChangeNodeStatusEvent({
    required this.ticketEntity,
    required this.ticketNodeEntity,
    required this.nodeStatus,
  });

  final TicketEntity ticketEntity;
  final TicketNodeEntity ticketNodeEntity;

  /// [TicketNodeStatus] cần update
  final TicketNodeStatus nodeStatus;
}

final class TicketSpamEvent extends TicketDetailsTabEvent {
  const TicketSpamEvent({
    required this.ticketEntity,
    required this.ticketNodeEntity,
    required this.params,
  });

  final TicketEntity ticketEntity;
  final TicketNodeEntity ticketNodeEntity;
  final TicketSpamParams params;
}

final class TicketMoveToOnHoldEvent extends TicketDetailsTabEvent {
  const TicketMoveToOnHoldEvent({
    required this.ticketEntity,
    required this.ticketNodeEntity,
    required this.params,
  });

  final TicketEntity ticketEntity;
  final TicketNodeEntity ticketNodeEntity;
  final TicketMoveToOnHoldParams params;
}

final class TicketGetOnHoldRequestEvent extends TicketDetailsTabEvent {
  const TicketGetOnHoldRequestEvent({
    required this.ticketEntity,
    required this.ticketNodeEntity,
  });

  final TicketEntity ticketEntity;
  final TicketNodeEntity ticketNodeEntity;
}

final class TicketGetAdditionalRequestsEvent extends TicketDetailsTabEvent {
  const TicketGetAdditionalRequestsEvent({
    required this.ticketEntity,
    required this.ticketNodeEntity,
  });

  final TicketEntity ticketEntity;
  final TicketNodeEntity ticketNodeEntity;
}

final class TicketAcceptOnHoldRequestEvent extends TicketDetailsTabEvent {
  const TicketAcceptOnHoldRequestEvent({
    required this.ticketEntity,
    required this.ticketNodeEntity,
    required this.ticketOnHoldRequestEntity,
  });

  final TicketEntity ticketEntity;
  final TicketNodeEntity ticketNodeEntity;
  final TicketOnHoldRequestEntity ticketOnHoldRequestEntity;
}

final class TicketRejectOnHoldRequestEvent extends TicketDetailsTabEvent {
  const TicketRejectOnHoldRequestEvent({
    required this.ticketEntity,
    required this.ticketNodeEntity,
    required this.ticketOnHoldRequestEntity,
  });

  final TicketEntity ticketEntity;
  final TicketNodeEntity ticketNodeEntity;
  final TicketOnHoldRequestEntity ticketOnHoldRequestEntity;
}

final class TicketCancelOnHoldRequestEvent extends TicketDetailsTabEvent {
  const TicketCancelOnHoldRequestEvent({
    required this.ticketEntity,
    required this.ticketNodeEntity,
    required this.ticketOnHoldRequestEntity,
  });

  final TicketEntity ticketEntity;
  final TicketNodeEntity ticketNodeEntity;
  final TicketOnHoldRequestEntity ticketOnHoldRequestEntity;
}

final class TicketUnfollowTicketEvent extends TicketDetailsTabEvent {
  const TicketUnfollowTicketEvent({
    required this.ticketEntity,
    required this.ticketNodeEntity,
    required this.option,
  });

  final TicketEntity ticketEntity;
  final TicketNodeEntity ticketNodeEntity;
  final UnfollowOption option;
}

final class TicketDeleteTicketEvent extends TicketDetailsTabEvent {
  const TicketDeleteTicketEvent({
    required this.ticketEntity,
  });

  final TicketEntity ticketEntity;
}

final class TicketReopenTicketEvent extends TicketDetailsTabEvent {
  const TicketReopenTicketEvent({
    required this.ticketEntity,
    required this.params,
  });

  final TicketEntity ticketEntity;
  final TicketReopenParams params;
}

final class TicketCloseTicketEvent extends TicketDetailsTabEvent {
  const TicketCloseTicketEvent({
    required this.ticketEntity,
    this.hasReview = false,
  });

  final TicketEntity ticketEntity;
  final bool hasReview;
}

final class TicketReviewEvent extends TicketDetailsTabEvent {
  const TicketReviewEvent({
    required this.ticketEntity,
    required this.params,
  });

  final TicketEntity ticketEntity;
  final TicketReviewParams params;
}

final class TicketCancelEvent extends TicketDetailsTabEvent {
  const TicketCancelEvent({
    required this.ticketEntity,
    required this.params,
  });

  final TicketEntity ticketEntity;
  final TicketCancelParams params;
}

final class TicketAddLabelEvent extends TicketDetailsTabEvent {
  const TicketAddLabelEvent({
    required this.ticketEntity,
    required this.ticketNodeEntity,
    required this.params,
  });

  final TicketEntity ticketEntity;
  final TicketNodeEntity ticketNodeEntity;
  final TicketLabelParams params;
}

final class TicketBackStepEvent extends TicketDetailsTabEvent {
  const TicketBackStepEvent({
    required this.ticketEntity,
    required this.ticketNodeEntity,
    required this.reason,
    required this.nodeIds,
  });

  final TicketEntity ticketEntity;
  final TicketNodeEntity ticketNodeEntity;
  final String reason;
  final List<int> nodeIds;
}

final class TicketBackEndStepEvent extends TicketDetailsTabEvent {
  const TicketBackEndStepEvent({
    required this.ticketEntity,
    required this.reason,
    required this.nodeIds,
  });

  final TicketEntity ticketEntity;
  final String reason;
  final List<int> nodeIds;
}

final class TicketGetIsTicketAssigneeEvent extends TicketDetailsTabEvent {
  const TicketGetIsTicketAssigneeEvent({
    required this.ticketEntity,
    required this.ticketNodeEntity,
    required this.ticketFlowChartEntity,
  });

  final TicketEntity ticketEntity;
  final TicketNodeEntity ticketNodeEntity;
  final TicketFlowChartEntity ticketFlowChartEntity;
}
